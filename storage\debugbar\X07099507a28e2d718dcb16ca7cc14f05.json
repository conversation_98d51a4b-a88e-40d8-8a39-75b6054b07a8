{"__meta": {"id": "X07099507a28e2d718dcb16ca7cc14f05", "datetime": "2025-07-12 15:47:39", "utime": **********.279983, "method": "GET", "uri": "/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752335258.776403, "end": **********.28, "duration": 0.5035970211029053, "duration_str": "504ms", "measures": [{"label": "Booting", "start": 1752335258.776403, "relative_start": 0, "end": **********.145387, "relative_end": **********.145387, "duration": 0.3689839839935303, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.145398, "relative_start": 0.36899495124816895, "end": **********.280001, "relative_end": 9.5367431640625e-07, "duration": 0.13460302352905273, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53401224, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.index", "param_count": null, "params": [], "start": **********.260277, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/index.blade.phppos.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.index"}]}, "route": {"uri": "GET pos", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.index", "controller": "App\\Http\\Controllers\\PosController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=37\" onclick=\"\">app/Http/Controllers/PosController.php:37-113</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.02111, "accumulated_duration_str": "21.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.182912, "duration": 0.01396, "duration_str": "13.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.13}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.205737, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.13, "width_percent": 2.937}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.22204, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 69.067, "width_percent": 2.653}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.224222, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 71.72, "width_percent": 2.037}, {"sql": "select *, CONCAT(name) AS name from `warehouses` where `created_by` = 15 and `id` = 8", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 52}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.229921, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PosController.php:52", "source": "app/Http/Controllers/PosController.php:52", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=52", "ajax": false, "filename": "PosController.php", "line": "52"}, "connection": "kdmkjkqknb", "start_percent": 73.757, "width_percent": 3.221}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'kdmkjkqknb' and table_name = 'customers' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 61}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.232655, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "PosController.php:61", "source": "app/Http/Controllers/PosController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=61", "ajax": false, "filename": "PosController.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 76.978, "width_percent": 7.627}, {"sql": "select * from `customers` where `created_by` = 15 and (`warehouse_id` = 8 or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["15", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 73}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.235628, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "PosController.php:73", "source": "app/Http/Controllers/PosController.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=73", "ajax": false, "filename": "PosController.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 84.604, "width_percent": 2.89}, {"sql": "select * from `users` where `warehouse_id` = 8 and `type` = 'delivery'", "type": "query", "params": [], "bindings": ["8", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 76}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2376351, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PosController.php:76", "source": "app/Http/Controllers/PosController.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=76", "ajax": false, "filename": "PosController.php", "line": "76"}, "connection": "kdmkjkqknb", "start_percent": 87.494, "width_percent": 2.416}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 521}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 78}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.240906, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "PosController.php:521", "source": "app/Http/Controllers/PosController.php:521", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=521", "ajax": false, "filename": "PosController.php", "line": "521"}, "connection": "kdmkjkqknb", "start_percent": 89.91, "width_percent": 4.358}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.index", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/index.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.26147, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 94.268, "width_percent": 3.411}, {"sql": "select `value`, `name` from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4078}, {"index": 14, "namespace": "view", "name": "pos.index", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/index.blade.php", "line": 6}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.2640681, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4078", "source": "app/Models/Utility.php:4078", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4078", "ajax": false, "filename": "Utility.php", "line": "4078"}, "connection": "kdmkjkqknb", "start_percent": 97.679, "width_percent": 2.321}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-156888122 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156888122\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.228744, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-316209008 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316209008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.240203, "xdebug_link": null}, {"message": "[ability => manage discount, result => null, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-444347810 data-indent-pad=\"  \"><span class=sf-dump-note>manage discount</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage discount</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-444347810\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.270439, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos", "status_code": "<pre class=sf-dump id=sf-dump-28641234 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-28641234\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-336449237 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-336449237\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1722115772 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1722115772\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1053120268 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIvQkxxeVQ2K1ZEU3RQWHNlSjFqQ2c9PSIsInZhbHVlIjoic0s1RU1sWTJEblRsMmlLcUhUU0t3MUwrUDZoZ0srREZSaFpmazBPRmtTWkxEM2VUZUpTK3dRYWI0RkNrVXJ3MnpGcGtsMExOVmo3SFhEYlBTWVI5NTkyczBYQXRaZnI2ZmI4U0RLekhKMFoweTJyNW5zUWlJUHRCLytoTkVSZFBrVUZ2TE5tRmJsNDArbmROSGVsUnNjd213aVRheVF4TDZzdy9tRUMzN2VxOVVEdEdaY0JobndzaXZObFhEaDBYa2k4L2tGUE5RS1ZUVHgvWmRqbG53cFo2bW95M2htS05KVml4N2EzNXBXVlNGVGMzVmw1YkFpa3ArRUFDT201Ky93ZkpLWTRadVo2SG4wRXBNVm1nUlNQdHNWdktsUTFTT3ZoSG94L1RIMEVIRWlwSG9DSDJHV2RmaVk5K3o4cDlobTFwWU5aWGNBZFdlc25XN1Uzd0h6VDZhWHl0ZmJyZ0xDYmsvczNHTFduMy9Wa01hcW9DVFJhMGsxTXM2aDQrMGJpYVRvbEZ4TFpMKzVQVmJRWmZpalNFbUtZd3RwVFN2dHJQM3poTm4xcnU2RXR3NnVUTU9rdlpZaEFGM0ZLMTJWYUduYmltNjlVaUt5Y3pjWlNobCs5VU9xU2tUTjdiTDJpUjlNY2VpQW9SeVBQM05RQktuaW14TjhTS0h6WEYiLCJtYWMiOiI0YzQ2NzJlNThmNmNmNWYxNjE5ZjE4YjA3ZjIyOTc2N2FkZGZhYWE0ZDc3NDg3Y2YzNzU5MmVjMDU0YTk0NzlkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBoNUs3ck9KS0dyQ3I1S2JsVWNtR0E9PSIsInZhbHVlIjoiZFJOanJnam83ZHRwNkYxNWNBSnZVQndic2p3L3Y5R0M1eC9tT3puSE0vYkEwYTczcEN1RFFudXVJaGoyNjhzN2grZnY1RGQvQUc4ak9PT1UyY2taempRUlkxbUhBb0lobjRsVHU5NTRXRVJPUWEzMFpzcVVuWHBPTU9QeUdXM2VYK25aalFzWENXZ3RqOTFBU1Z0b1E1a1AwTnB5M0psY01JN3BTWFRUTzNKbDRkNzBUNXJucUg5UUk0K3hUaUZyQTlnaXQvWC9JSE1zTzNtTGVmWWVQOUo2NXYwdVB4VlNFZXpJTno3c01hSWJ0aFZ0VlJzWnlKMnpXM3gxeXVORS8xN1MxQ2xGcC8vbThSWDlSNlY1VHlIbUNpcGV3TWtVTHREZDFtOXUyRlFXRlVQZ09uVGF3d29IYzJsUW9jVkMzN2dHREZscDVvWW5qdXBKV1F6eEptbnF4QUhKdXE1NXRrVktXdUlaWHdNZU1lbkRweGRscFZDUGI3bThvY1BJcXVONEFLLzl6MER3alNneTZGT0cwTWtuanRnZjhBVmxYLzFrN042Y1NuNXNHN0g0OWdWa1pWaGZJQWMrZ1BZN080T2szR3J6aWUxOHVkSnk3YnhUVjZiaHlHZmQrNWoxVGVuRDlBR3lsMktjV1lwRnhzUDhaamcrWURQcnZtMXYiLCJtYWMiOiJkN2YzOWI0M2U4NDgwMzNiZGQ1MDAyNjYwNzRiNGY2YzdjZTVjMjg0YjBhNjY1NTE0ZDFiMDRkMjg0MmNhNGFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053120268\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-719059465 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719059465\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1272081908 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:47:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZYbzd3UnZpWTdQazNpVzRnVy92a2c9PSIsInZhbHVlIjoiSUUvWSt0THVjd0tKNFBGTHhrelJBRHRJbThsSmt6YUFXNWNySUw5UTk3a0luWC9XYUZIbXBKR0FMNHVVZFE0MGZJTHQ2NGZleDdscEVkTHNJVWJHdjdFZmZucXF4ZStNZEdjNUt6QllTd0IwSVRuSGVNQTU4ZHNwT2k3aDZBWlVFQ3p2ZHlJclIzcnprSjZ4Y3AwOUtzNEVCalFjUWhGcnFTQm5DM1F2RWVuaXZrV1RkdTNxcUYrYUI2d3R2VTBaOVlpZWE4MStMais4a011WWRGTU02dGtEcm1Fek9IQjVLMWIwNm1mV0RWREJuUHZpZ0MyWWVoTlk1d2s0U1dvZU1XZFRCbVc2NUtPektxQUM0MG9iVWhiNUlDVTNGR0Y2cFlSZHR4ZWt5N1h5VHlJa29VTERodjc4TTUxS1NTTFhpVDF3Y3Bkc3NmcTlBL2dNbVRjZEtGK3lOM1ZkZmVMOUFBQitmRytlWDQraHhOOVFUQ1lRcWVyWnhHdEhESTY0TVhOck1GRU5oTC8rSHUxUGtWbGlUeTlaYjV4YnBTOU05bmtTWXZXMTkxUy9HNm95cUhHQmY2UGJhV0pRZFVwWWxqdG5wdWJ3RXN4M0MwT2QxR1A2RGxFZ0dNNlZQZUI0WCtRUERmZk1nVVRSdit0QnRJNUpoWmVFSjFuOHJodVoiLCJtYWMiOiIxMzdkOTlhM2Q3ZjY4YmM1MmFkZmJhZTZkMmY1YzFmNzYyMzVmNWZiNWQzOWQ1MjJkZmU4ZDNmOTA4OTE5ZTQ3IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InYzY2xtR0tlOFI4MndQMTNLTTNNd0E9PSIsInZhbHVlIjoiZVNYOGZ4VGwwa0dtQ256MzhRd29mLzFPak5ObWFIUEhlbUVHY1VxYk5mUS9ORTlUTXhSeUN6WWtiM0RlWTV3bE9DMzA2eDRYRm1tNXNRb2p0WXJZZzNtemxBZ1h0cjBZNHIrNDNQOHJFTjJKNWx6b3V4TWFDV0JXL1V4dFdPbXpRdmxtSXVRdzQ1QXFUdTQxRmxadDJ2NjFqeDNsRElYM3A1dkh6SFRpODlwN2hJcTZ5VzhKTTdCNTQxeUFPbmErUkdrZHU2M2s3RDN1Y3JrbWlCRlZUMTIyMHhSb0tNNHMrTWcyUUV5eXptbk81amdKaFpiK01NVjFxcUtCRWdxaVk5QlpCRERuVEl6bXduTllnTVJqeENPZjBVSWlVRVBLb2svMVFSODM2SEgwcjE0cWRhMUo3ZWxyclBXYW4wYXNNZDR3ZnUzWkhFWHE0bGJNTFFoV0JRSERQSEY2UjhQOXNyTkhHdzNqenhoUDhGcGpkNm0zNWVWMXhPbFhJZXdpVGMzRTExanhZZXcxTzRDTmplMmk5a3ZpcTNaMGlnTy9hUmVobkYwTFJnWDRqNTloOTVSNXJEdTM4Zzh6L1liNHdNdFFmRFZtMGtqUUtqVG1leUtrbThyYmwxWjlXMWQxb0FpWUYxdUoxQjJKbmRjUWNLdjVSK2ZNNnNGcjNlMDYiLCJtYWMiOiJhNzRjY2M2NjVkNmU1MTVhNDBjMGM5ZDhkMTY1NTVhNjQxZDhkMzI3ZjQxY2ZiMzljOTU1ZWYwZjJiN2EzZWY5IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZYbzd3UnZpWTdQazNpVzRnVy92a2c9PSIsInZhbHVlIjoiSUUvWSt0THVjd0tKNFBGTHhrelJBRHRJbThsSmt6YUFXNWNySUw5UTk3a0luWC9XYUZIbXBKR0FMNHVVZFE0MGZJTHQ2NGZleDdscEVkTHNJVWJHdjdFZmZucXF4ZStNZEdjNUt6QllTd0IwSVRuSGVNQTU4ZHNwT2k3aDZBWlVFQ3p2ZHlJclIzcnprSjZ4Y3AwOUtzNEVCalFjUWhGcnFTQm5DM1F2RWVuaXZrV1RkdTNxcUYrYUI2d3R2VTBaOVlpZWE4MStMais4a011WWRGTU02dGtEcm1Fek9IQjVLMWIwNm1mV0RWREJuUHZpZ0MyWWVoTlk1d2s0U1dvZU1XZFRCbVc2NUtPektxQUM0MG9iVWhiNUlDVTNGR0Y2cFlSZHR4ZWt5N1h5VHlJa29VTERodjc4TTUxS1NTTFhpVDF3Y3Bkc3NmcTlBL2dNbVRjZEtGK3lOM1ZkZmVMOUFBQitmRytlWDQraHhOOVFUQ1lRcWVyWnhHdEhESTY0TVhOck1GRU5oTC8rSHUxUGtWbGlUeTlaYjV4YnBTOU05bmtTWXZXMTkxUy9HNm95cUhHQmY2UGJhV0pRZFVwWWxqdG5wdWJ3RXN4M0MwT2QxR1A2RGxFZ0dNNlZQZUI0WCtRUERmZk1nVVRSdit0QnRJNUpoWmVFSjFuOHJodVoiLCJtYWMiOiIxMzdkOTlhM2Q3ZjY4YmM1MmFkZmJhZTZkMmY1YzFmNzYyMzVmNWZiNWQzOWQ1MjJkZmU4ZDNmOTA4OTE5ZTQ3IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InYzY2xtR0tlOFI4MndQMTNLTTNNd0E9PSIsInZhbHVlIjoiZVNYOGZ4VGwwa0dtQ256MzhRd29mLzFPak5ObWFIUEhlbUVHY1VxYk5mUS9ORTlUTXhSeUN6WWtiM0RlWTV3bE9DMzA2eDRYRm1tNXNRb2p0WXJZZzNtemxBZ1h0cjBZNHIrNDNQOHJFTjJKNWx6b3V4TWFDV0JXL1V4dFdPbXpRdmxtSXVRdzQ1QXFUdTQxRmxadDJ2NjFqeDNsRElYM3A1dkh6SFRpODlwN2hJcTZ5VzhKTTdCNTQxeUFPbmErUkdrZHU2M2s3RDN1Y3JrbWlCRlZUMTIyMHhSb0tNNHMrTWcyUUV5eXptbk81amdKaFpiK01NVjFxcUtCRWdxaVk5QlpCRERuVEl6bXduTllnTVJqeENPZjBVSWlVRVBLb2svMVFSODM2SEgwcjE0cWRhMUo3ZWxyclBXYW4wYXNNZDR3ZnUzWkhFWHE0bGJNTFFoV0JRSERQSEY2UjhQOXNyTkhHdzNqenhoUDhGcGpkNm0zNWVWMXhPbFhJZXdpVGMzRTExanhZZXcxTzRDTmplMmk5a3ZpcTNaMGlnTy9hUmVobkYwTFJnWDRqNTloOTVSNXJEdTM4Zzh6L1liNHdNdFFmRFZtMGtqUUtqVG1leUtrbThyYmwxWjlXMWQxb0FpWUYxdUoxQjJKbmRjUWNLdjVSK2ZNNnNGcjNlMDYiLCJtYWMiOiJhNzRjY2M2NjVkNmU1MTVhNDBjMGM5ZDhkMTY1NTVhNjQxZDhkMzI3ZjQxY2ZiMzljOTU1ZWYwZjJiN2EzZWY5IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272081908\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-876322854 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876322854\", {\"maxDepth\":0})</script>\n"}}