{"__meta": {"id": "X1b20273be686e09ee44aeca2282c80b9", "datetime": "2025-07-12 16:00:17", "utime": **********.399259, "method": "GET", "uri": "/add-to-cart/2421/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752336016.904626, "end": **********.399273, "duration": 0.4946470260620117, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1752336016.904626, "relative_start": 0, "end": **********.306683, "relative_end": **********.306683, "duration": 0.4020571708679199, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.306692, "relative_start": 0.4020659923553467, "end": **********.399274, "relative_end": 1.1920928955078125e-06, "duration": 0.09258222579956055, "duration_str": "92.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673384, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.008300000000000002, "accumulated_duration_str": "8.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.343117, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.711}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3555648, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.711, "width_percent": 6.867}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.371344, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 34.578, "width_percent": 10.241}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.374579, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 44.819, "width_percent": 6.265}, {"sql": "select * from `product_services` where `product_services`.`id` = '2421' limit 1", "type": "query", "params": [], "bindings": ["2421"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3800519, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 51.084, "width_percent": 6.867}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2421 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2421", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.384203, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 57.952, "width_percent": 36.024}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.389218, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.976, "width_percent": 6.024}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-849354311 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849354311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.379049, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2421 => array:9 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"2421\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2421/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1769384439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1769384439\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2081927434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2081927434\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRnbExBQ2lyQXZXNWhpUDRmZkFvdlE9PSIsInZhbHVlIjoiaGF4OEFMQmI5SDhMMTNQMGF6V0xySHVCby9wZVM5VXpZdjVFQTgzVmtkVXVOYVVmblh0M2dPQnByVGgvRk9zQ0ErNGxMTWEyK0Y3bVoyaTBhMXEvY0ZOWmxwdHpJajhlUEdDdXN0Tk5nMmh2NmtqUk5NQTBtNW5Sa3FxNEk2SjRxSU5KdzZ3azdUd2IxNHNpdmY2eVFJZ0QxTTZ0T0tMU2QybmtBMUE3STREOGxqM1A3MHVCOFc2dEV4UlF4V2VIUk4wUHFKaXU3VGIwR3gyQ2lYazNjc0U4Y0hpU2tFTExTaUgyYnlpb1ozWUgrQUUwVXdPcEx0ZHRqakl5V2hhL1BVeHgwMVl2a2dhVnNoZUs4TlhWQThKM0RpampKL0RCUFl5K3VvMFJha0xPbzYzdElCUDdCTTYzVlBlUnBERW9hSjFxM1A3cXF3SFNqTllyVGYxWFRPajBtSHpTWldEWU9QYWsrY0gwNFZxNTV5R3NzS1N4NHJaN0VxNEV0ZDk5ZUtzaVRMbGppOXRuNkRvTUtLMS80TW53aFNpTGJXdzhaazhnL3Z0TnEwMkc5ajh4ellRUHlPQU5vTVQ3VjlsSkVVdmhXZktDNk5hVXNjMWJsWGFrdWVLNXFsVDJtR2RySS9xbWpkM212K1lhM01rQVB4SXU1NWNST3hHb09raFMiLCJtYWMiOiJmOGYwNzZmMWQzOWZiMWEyZWI5YTkzMDJmYTk2ZjMzMTA1YmYxYTUyZTMwYzA5OWQ2YWE5Njk5ZjM0YWIyYzE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdEL3pvQThCUU1BSm1JV0U3eUFRa3c9PSIsInZhbHVlIjoiTWdPaEwwZ0FOb3FYWjNzTkkveW1mWE4vNlBQajJpZVZ4VmhHYkZoQm9oYlpqc0VERGpIcnlpdkYvTjkydmErOTdKYlNmZ1g5Y3gxN2F5OWdyMGZiSjVWQVVzU1NXODBLMmFxemRlTFlUSThFanh6eVdiZGlvR1kxTGJlQjdDaFQva3Q0TjdXZ2k1MnlwTk1MOENEZTNVcWhTQjM3bWtqdEdOakNReHBleHpYeWp6YkhoaWNoSWk1K1poN0NQVjQ4OUU2RldBOHc2dTVvdUloQTJUeHFabHJKVmtZS0xvajVXWllPMVpERzNuc2krWHZNYU1ZeUdheWV3ZWZoN3h0SHNEWW5GZmpldml1YVIyNHpzRzRyVTNFOEQ2YmplSTBKMVl3NlJiM1ovZVdUYVN5WUF6bkIzcTBuQllNdmcreEU1M2VzSFhQS2o3NndrUWlGLzQ5TldISm5uZ1JRdHNCRWpGdmNRL1d6Z3hsVTQ1RkMvSVR4eXFzOFRCNjFZeFhEQXNJWDhyL1RhYTBpaXdZeC9LQ0ZJVGEyZWQyZ2pEZ3ppdW9zamxpZUovVFNKRXhxUmd4ZVJCTWFCRnU3djBXYzc4KzVqbjVseDljTitOckVNazRCTWZpK0E3WWo2ZGRtc0JGdjZ0MVJUamJmOGNvdlBhbm16U2twK0FlN2JYelAiLCJtYWMiOiJkODE1MWM4ZWMwNTA3ZGEyM2Y3ODQ5MGU1NGIzY2ZkMmJlZDNmNzU3M2FmNjgyODBmYTY1MTkyYzY4ZmM4Y2RiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-616530768 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616530768\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1705816636 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:00:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRGUlJzUU5lUGlhaGNGNWRlMkNxaHc9PSIsInZhbHVlIjoiNjEySndkQTVwUVhzaGZuQzFEMVZQMmpQY2NCVExVaUVlWitSQjNKeTQvQlNOeEFWSEJWV20zL2VlRlhaWStjYVh3TFYvY1RLbUdJOFF4K0loV2VBR1p4OEZidThaVVF4L0VoQnBWd05jNWEra2tlcDVRays4WG5nWG9QczRQZW9OdEEzbHR2RVR0RmR1RFRYT2tHM1kxM2RjYVllbzk4WklnamVzNmxjMUhwZmVrVkJCTDZLbzdlZUlmNFhWSEZoTlpjVVd4QTdKN0NlMUFQWXJma0VyRjRmaWo5N3duaUQ5dnJsNS9UbXBDZnhRYzEzZUZzL0ZJbmlaaFFtbEhWZ0ErdEduZ3FYOG9JTXhvY0RML2c3aUxwT2t5Z3NNRmxCWnpVRW8yWjNjbmpVZTFXdlRGMkRTU3RXUytPVXlQODRrdmY0bjVndXZWSGhRNkNxK0RiYmRkbmZCR2hNTWREdi9KVys3Y25ONGJEcGNhbFhGM0twSVN6dVRyaEg3SXhWWkVGUmRTYWI2Mm0zRWMxaGMwbXNEY2JlUkhZcitOVnVFcHhzcVl1MzVhWTBCOE9hWDdVWXBZbnZtdmZ4dktFN2hRWERvVlhHNCtUNEFESkNCUHh1UWh2ZFVDK2c5bTNFZ0VHTlpkVTZwMXhMUjRWRHg4N25KYnRNa3VzWTBkN1QiLCJtYWMiOiJhY2RjMGIxZTRlZTQ4YTdmZjJlZTlkYzcwNDNiZjdkMTg0MThiYTMwNjFjZDk4NTI3Y2I0YjI1ZjlmYzg4ZDA3IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:00:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InQyUFp1dlIzUmF0SmtqUGFTbStybkE9PSIsInZhbHVlIjoieE1SZlVxVFVOc3lmbXcydmZBTkJtV2tvZXJMbW02NzZ3U1l3cXY0VVA3UmlwZkU5R3A0RGUzTDZxR0oxcTAreHBwRmpxdDBKUU1XdVVMU1Uwa3E2QnM3WS9WM2ZzNTg3eFhCU0lkYkNpTTNuTlV6VDNlbFd2Um5NMWc3dVJ2dUdpK2JGTXB3MHlzRXdqSnZ0dW1tSjBhTjhYQUVOeWsyKzVMWGg5bEFWeUw4SFZmaGdFbnBPOG1WS2h4UmtjbE0vU2JnNnlERC90NER2cXU4Y3VtRGFNQ3Y0dksyNlQ5UTdyRjY4SHczRnB2SmJxUE5pbWlMZCtwb2dXOExrUmkwcWxqS0VnSERWcWhZOHhsM1FZdFA2elVDbXRMcUh5K1Q1a3ZxN3VSbHg3alJJOFllVDJiTDJCRTNMdlFYclhpQXRiWlArVCtzTGthZWdwNTdGY0xoS2dmU3ZpZDUwUkhqbDhGNmo0d2wxTldqQTB2WTMxZ0wyQ1hxR2h5NUtDZTNnSDhQOXh3SitLcEt3ZFpMaFZuOVB6Njc0cWhyQU45Zk9UQUdWKzZWdjh0Y2RyQ0MzSGwySXhXVEk1blJDbGZNR0cvaVdrWk9zUjJBaDdZa1p5ckVmQ1l1eFJSRXNNMUVjOVNNa3lOL3NMYmVGcXlkTndyQWJsenU5YWVRLzFMRmYiLCJtYWMiOiI1YmIyODQ0YTZlOGUxYjYxNWMxYmU1OTk3ZWY5ZTE3OGQzNmJmMTg1MmVmYmYyODUzZjdlNWJjMTNhYjU1ODQ0IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:00:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRGUlJzUU5lUGlhaGNGNWRlMkNxaHc9PSIsInZhbHVlIjoiNjEySndkQTVwUVhzaGZuQzFEMVZQMmpQY2NCVExVaUVlWitSQjNKeTQvQlNOeEFWSEJWV20zL2VlRlhaWStjYVh3TFYvY1RLbUdJOFF4K0loV2VBR1p4OEZidThaVVF4L0VoQnBWd05jNWEra2tlcDVRays4WG5nWG9QczRQZW9OdEEzbHR2RVR0RmR1RFRYT2tHM1kxM2RjYVllbzk4WklnamVzNmxjMUhwZmVrVkJCTDZLbzdlZUlmNFhWSEZoTlpjVVd4QTdKN0NlMUFQWXJma0VyRjRmaWo5N3duaUQ5dnJsNS9UbXBDZnhRYzEzZUZzL0ZJbmlaaFFtbEhWZ0ErdEduZ3FYOG9JTXhvY0RML2c3aUxwT2t5Z3NNRmxCWnpVRW8yWjNjbmpVZTFXdlRGMkRTU3RXUytPVXlQODRrdmY0bjVndXZWSGhRNkNxK0RiYmRkbmZCR2hNTWREdi9KVys3Y25ONGJEcGNhbFhGM0twSVN6dVRyaEg3SXhWWkVGUmRTYWI2Mm0zRWMxaGMwbXNEY2JlUkhZcitOVnVFcHhzcVl1MzVhWTBCOE9hWDdVWXBZbnZtdmZ4dktFN2hRWERvVlhHNCtUNEFESkNCUHh1UWh2ZFVDK2c5bTNFZ0VHTlpkVTZwMXhMUjRWRHg4N25KYnRNa3VzWTBkN1QiLCJtYWMiOiJhY2RjMGIxZTRlZTQ4YTdmZjJlZTlkYzcwNDNiZjdkMTg0MThiYTMwNjFjZDk4NTI3Y2I0YjI1ZjlmYzg4ZDA3IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:00:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InQyUFp1dlIzUmF0SmtqUGFTbStybkE9PSIsInZhbHVlIjoieE1SZlVxVFVOc3lmbXcydmZBTkJtV2tvZXJMbW02NzZ3U1l3cXY0VVA3UmlwZkU5R3A0RGUzTDZxR0oxcTAreHBwRmpxdDBKUU1XdVVMU1Uwa3E2QnM3WS9WM2ZzNTg3eFhCU0lkYkNpTTNuTlV6VDNlbFd2Um5NMWc3dVJ2dUdpK2JGTXB3MHlzRXdqSnZ0dW1tSjBhTjhYQUVOeWsyKzVMWGg5bEFWeUw4SFZmaGdFbnBPOG1WS2h4UmtjbE0vU2JnNnlERC90NER2cXU4Y3VtRGFNQ3Y0dksyNlQ5UTdyRjY4SHczRnB2SmJxUE5pbWlMZCtwb2dXOExrUmkwcWxqS0VnSERWcWhZOHhsM1FZdFA2elVDbXRMcUh5K1Q1a3ZxN3VSbHg3alJJOFllVDJiTDJCRTNMdlFYclhpQXRiWlArVCtzTGthZWdwNTdGY0xoS2dmU3ZpZDUwUkhqbDhGNmo0d2wxTldqQTB2WTMxZ0wyQ1hxR2h5NUtDZTNnSDhQOXh3SitLcEt3ZFpMaFZuOVB6Njc0cWhyQU45Zk9UQUdWKzZWdjh0Y2RyQ0MzSGwySXhXVEk1blJDbGZNR0cvaVdrWk9zUjJBaDdZa1p5ckVmQ1l1eFJSRXNNMUVjOVNNa3lOL3NMYmVGcXlkTndyQWJsenU5YWVRLzFMRmYiLCJtYWMiOiI1YmIyODQ0YTZlOGUxYjYxNWMxYmU1OTk3ZWY5ZTE3OGQzNmJmMTg1MmVmYmYyODUzZjdlNWJjMTNhYjU1ODQ0IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:00:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705816636\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}