{"__meta": {"id": "X25f76be48bd2ec8c73ede79285af767c", "datetime": "2025-07-12 15:59:29", "utime": **********.375916, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752335968.963168, "end": **********.375928, "duration": 0.41276001930236816, "duration_str": "413ms", "measures": [{"label": "Booting", "start": 1752335968.963168, "relative_start": 0, "end": **********.30532, "relative_end": **********.30532, "duration": 0.34215211868286133, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.305329, "relative_start": 0.3421611785888672, "end": **********.375929, "relative_end": 1.1920928955078125e-06, "duration": 0.07060003280639648, "duration_str": "70.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48185424, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.006499999999999999, "accumulated_duration_str": "6.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3351738, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.077}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3448968, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.077, "width_percent": 6.615}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.357737, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.692, "width_percent": 7.385}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3594582, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 39.077, "width_percent": 4.154}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3633819, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 43.231, "width_percent": 36.308}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.367712, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 79.538, "width_percent": 20.462}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-122996157 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-122996157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.362521, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-289390234 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-289390234\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-538127353 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-538127353\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-621829511 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-621829511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1900122629 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFYTVRtemhycnJmQnFlenJLNDhDd3c9PSIsInZhbHVlIjoiczVtRjZOQzFtMWc1azdlM1hNVll4QUcvc21KcysvRGk1U01ZR0pKSTlWREhXaEVZaVZZTDNacDdSU1FkeWdtRmM5c3MxUjAzZWZBOGhhbzV5dTlaamJZWjl3dlhJVVQwMHlDWlFTWkFGZ0UyT21qcmxtc3N5K2JXL0hKU2F3MDBhVExCWXc1Z0RsazdlTXZrQkNyOWlFcVUwNys3V1JVUjRpWUsxeWE5MXN5SU1kSUxFVGRJS0IzYm8ySmJTRFcvTzFkNnZaTEJ2blBYcFJTVVZkK1lOOWxPZ2s3TE1aenhtNW1nNW9CblVrQUtaMzc2dFlPNHFuUkZvUkN3a2xuZlRjK0NNTkZUUk05SFJKdFoxbnRud0RnMmJrSmwzN1lzK1RIUzN0UFFWTUdUcDNjc2ZIWnNuNys0MUYxaXZoZVpnMGNKbFZoTzhaaUMrWklVSmhlcVRwVUh2KzhEMkdWZk1tb1RIbno4N0phVDBjN3RGTWpTWFNHTTRGZ0pmNjZYaHZGWWFnM1JFT0VPSHpTYmFEOFBnVW5wMC9UWkhnZ1VCK2owYWlsdHNmblU2aDBmV3lRazNNZ0E1bWV5VHFMUHl6WGtkbjVwVXdlS2ZrN3psYTFHdnZWcUR4TzlMYTVHZmQ0QmhoNzgrN3JIZW5MVmEyZ1JyRnJNVFErdWRqYm4iLCJtYWMiOiJmNjFhZjU2YzU5Zjg4ZGZjYjAwMGY3NTM0YTU3ZWZkNDY4NzVjNzMzOTk4MDMxYWQ2ZDE3ZWY1YWRlZmVlMzg0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhtcWdTQ2dTQ0FnbUcwcXlCYlJzQUE9PSIsInZhbHVlIjoiYjlBRHRjY3NtQ0pxK09kQlZYSnU1UjhBRkF0LzV2aUhidlFLc1NSZStLS0FXbkIyeGZ2dDFXUjFvNFZmVk9oNDNNM0RxTjhHemYvQW1uNyt6ak9YZkVvMzlVNXVzaFBHcVBkZTQ5N0ZCaCs3YjRRc1V0c01vK1NVRk1aRHJaeEdDVW9xZFFFQTJVNnVHWGplVWZiVHB0Y1FWUXpIYWo3dC9qQkk5SmttRjFIRHhDUFRNUmlvdU05ZzdYRnpreGVueWhWNGlXWUdkbkJ1d0dYUjlYajlQSWpzL1FDY3NtS3pTQ2VLZzliOTFZODUvbWxmTWJxaUgyeWt5Tjk3TTZHTzZpd3RINWFycDJYbU9aMkNTYU5qNWJZNWt3TVNTTGZCQ2dlQlp2V1FmQjZIbzFwTFh3TER3WkJOMmpzaUhoOWljQjlIajI4bUlVYmpLaVY0ak9oQkphRy9FdlVMTU5UNmZUU3haVDN0ZzF6SUhGcmVUYUNXVVIrU0J6SzFGdUtpWCtwOWM0bFZ1Qkp0ckxhcW5qNHhqNzY4clVlcjR0cXZiSUF6REFBanhVUk9HbU1WTEdzQnNtSU0ySmRiMWxMRXhZdFdObXFoSFV3QisvTXVHajRia1c2OGdhYU0vVzBDSDJoLzRZdzJsbWZFaVFnS1JtTEJHdU8wUmlyU29COWsiLCJtYWMiOiJlMWVjNDBjOGM1NWI3NmZlYmMzNmVjNjEyZTkwY2Q1ODZkNTMxMjM3YTgwNzk0MGE5MzM3ZDM4NTEzYTY3M2UwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900122629\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1169401431 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169401431\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-792599804 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:59:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZ1UTAwRkIzRmFNY1VrZ1lZRThqY2c9PSIsInZhbHVlIjoiTlhGQ3ZUblRXOTFhYXdsdC9VSFZRcUtxbUpKR1BzKzhrcmdsdzZ6bk9xZnc4UTc1M25nWUE2Y21Bdi90Zmw2dVBJd3ZwT2FqU1V4KzE2MmhYc2Z0Zzc2eFhEWGg4bnVRaTUwQSsxNTJ1Nm9VNk5xcVF5YWl1dEUxdERrelpFVnI4OUdZcmNzKzlSWkhLYnpydDA1WVZkWkVhUUdLWHFhM0ExOTQxY0xkcndsZjhkT1VGcjBUcmV6ejJFQU45QXFBUDJpYTZGR2RkUEt3RUpTOERVc1ppM2owTTE4cFVCUzJhNDVLWm5FM0tDWWw4ZWlFV29LejM3c25LR21BMUlFVW4rVVRybDVrRW5FRHpFRno2bGM3UDAzM1k3dU1ZTWtvNWo4WHJjSUFTZFpxU1pMcVZsYm9wOWpJU05wNGhFQ3VRRWRiZ1g4dEliZU1SbUdkRXFBbGFGU3BWa0tXV1VoUkwwQ0Vsb2gyTzdGdE15OHYvcmVjOTNqVzkxSVc0OSt3aSs1dnFpRCsvTHIrWHJJdTgyOCsyaURHeGVYRExrZ3Z2WENlYWpEQzRiMGtCUlU0cS9BNW0zczZYYVg2MVpieEZWN1V0UFJ5ZW0rWkR3REtWZDVnMmRqeVhEUjFLMk5OdDEvT0p3dGZYaWFMazZwTmVQUDNUUGJqZXhPVTUwb2wiLCJtYWMiOiJhODdkYjVkOWU3YWUzMzNhZTAwOTFlMjAwZDA0NzYyYjZlOWE5NjlhNjlhNTdlNDQ5NTQ2ZmIwYWU1YzZlOGFhIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:59:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imk2VmVodExCdmlZeC9uTy9QcTFrWXc9PSIsInZhbHVlIjoiNzJLTVNxZUV6clp4c0lITHBNRXc0cU5FbE5xVjR5bTJNcGR0SEhkV3JVMGFVS1N0Y0lqWVR4UnJoWTRpWkZBU0hiMUVyVGlJY3dlRExJdFdoa3hQU0s1UDZOays3Ri9aZVliNjBheE5ZMHo5V05QQnJjMWFmVjhCSXZKaVEwcGVYQ08wdFBMZWFsSjVveG81MUJGdlhmWm83RTBGdUN1cDJEMDZJWVlLaEZsRE5ybmZGTG9IZUVLWVFIbTdxckNJNWUxS0dKOGhScEJYazdGczNWczlpOE5EMjJoYkg5VTJLazE5SktOTld1cjR3L3JjL1I5SklhWitrTks4NmJ4bEJSbGViT0MyVmFtcnh5WUF0QXptVXNGckZGc2VyeGlBa1RSRkkyOHRIZnduVFlIa29aaHRyL2xpMDF1ZUJyVzh2TWcrMEJtdXNTcXdQV3VwaWlUYnowR0VLUGlwbjA4NDJOeFVjVkdPeWp2TmFEWTE1ME81dFNPaGJHYkZSTlhHV0k0cGdZWTBDY29SZktleVhzZTJueTJacDJWK0QyMEk2YnlVM3dsQVpOanpOdnNjaER5Vkt1blpWcHRkcGRuWW5sYU45a1JYN1l0SzlFc1NvZG1VYmJzKzltWmo0dVQ5RkZ6a056MmFHb084QkFESHMvSm5pcmlGeCtRQlZvRTgiLCJtYWMiOiJhNTEyYTdiYTBjYjkyZmVkM2I5MDM3OTlkZWZlOGY1YjcwZDI0OGUxNmQ1ZjdjNjk2MWE4ZGMwNDFhMzQwYTJjIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:59:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZ1UTAwRkIzRmFNY1VrZ1lZRThqY2c9PSIsInZhbHVlIjoiTlhGQ3ZUblRXOTFhYXdsdC9VSFZRcUtxbUpKR1BzKzhrcmdsdzZ6bk9xZnc4UTc1M25nWUE2Y21Bdi90Zmw2dVBJd3ZwT2FqU1V4KzE2MmhYc2Z0Zzc2eFhEWGg4bnVRaTUwQSsxNTJ1Nm9VNk5xcVF5YWl1dEUxdERrelpFVnI4OUdZcmNzKzlSWkhLYnpydDA1WVZkWkVhUUdLWHFhM0ExOTQxY0xkcndsZjhkT1VGcjBUcmV6ejJFQU45QXFBUDJpYTZGR2RkUEt3RUpTOERVc1ppM2owTTE4cFVCUzJhNDVLWm5FM0tDWWw4ZWlFV29LejM3c25LR21BMUlFVW4rVVRybDVrRW5FRHpFRno2bGM3UDAzM1k3dU1ZTWtvNWo4WHJjSUFTZFpxU1pMcVZsYm9wOWpJU05wNGhFQ3VRRWRiZ1g4dEliZU1SbUdkRXFBbGFGU3BWa0tXV1VoUkwwQ0Vsb2gyTzdGdE15OHYvcmVjOTNqVzkxSVc0OSt3aSs1dnFpRCsvTHIrWHJJdTgyOCsyaURHeGVYRExrZ3Z2WENlYWpEQzRiMGtCUlU0cS9BNW0zczZYYVg2MVpieEZWN1V0UFJ5ZW0rWkR3REtWZDVnMmRqeVhEUjFLMk5OdDEvT0p3dGZYaWFMazZwTmVQUDNUUGJqZXhPVTUwb2wiLCJtYWMiOiJhODdkYjVkOWU3YWUzMzNhZTAwOTFlMjAwZDA0NzYyYjZlOWE5NjlhNjlhNTdlNDQ5NTQ2ZmIwYWU1YzZlOGFhIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:59:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imk2VmVodExCdmlZeC9uTy9QcTFrWXc9PSIsInZhbHVlIjoiNzJLTVNxZUV6clp4c0lITHBNRXc0cU5FbE5xVjR5bTJNcGR0SEhkV3JVMGFVS1N0Y0lqWVR4UnJoWTRpWkZBU0hiMUVyVGlJY3dlRExJdFdoa3hQU0s1UDZOays3Ri9aZVliNjBheE5ZMHo5V05QQnJjMWFmVjhCSXZKaVEwcGVYQ08wdFBMZWFsSjVveG81MUJGdlhmWm83RTBGdUN1cDJEMDZJWVlLaEZsRE5ybmZGTG9IZUVLWVFIbTdxckNJNWUxS0dKOGhScEJYazdGczNWczlpOE5EMjJoYkg5VTJLazE5SktOTld1cjR3L3JjL1I5SklhWitrTks4NmJ4bEJSbGViT0MyVmFtcnh5WUF0QXptVXNGckZGc2VyeGlBa1RSRkkyOHRIZnduVFlIa29aaHRyL2xpMDF1ZUJyVzh2TWcrMEJtdXNTcXdQV3VwaWlUYnowR0VLUGlwbjA4NDJOeFVjVkdPeWp2TmFEWTE1ME81dFNPaGJHYkZSTlhHV0k0cGdZWTBDY29SZktleVhzZTJueTJacDJWK0QyMEk2YnlVM3dsQVpOanpOdnNjaER5Vkt1blpWcHRkcGRuWW5sYU45a1JYN1l0SzlFc1NvZG1VYmJzKzltWmo0dVQ5RkZ6a056MmFHb084QkFESHMvSm5pcmlGeCtRQlZvRTgiLCJtYWMiOiJhNTEyYTdiYTBjYjkyZmVkM2I5MDM3OTlkZWZlOGY1YjcwZDI0OGUxNmQ1ZjdjNjk2MWE4ZGMwNDFhMzQwYTJjIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:59:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792599804\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2116164451 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116164451\", {\"maxDepth\":0})</script>\n"}}