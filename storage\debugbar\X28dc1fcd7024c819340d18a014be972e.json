{"__meta": {"id": "X28dc1fcd7024c819340d18a014be972e", "datetime": "2025-07-12 15:47:14", "utime": **********.719253, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.274374, "end": **********.719265, "duration": 0.44489097595214844, "duration_str": "445ms", "measures": [{"label": "Booting", "start": **********.274374, "relative_start": 0, "end": **********.629545, "relative_end": **********.629545, "duration": 0.35517096519470215, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.629553, "relative_start": 0.3551790714263916, "end": **********.719267, "relative_end": 1.9073486328125e-06, "duration": 0.08971381187438965, "duration_str": "89.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184832, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01841, "accumulated_duration_str": "18.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.662379, "duration": 0.01218, "duration_str": "12.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.16}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.682905, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.16, "width_percent": 2.553}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.697036, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 68.713, "width_percent": 2.607}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.69878, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 71.32, "width_percent": 1.521}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.703352, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 72.841, "width_percent": 17.871}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.710083, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 90.712, "width_percent": 9.288}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-907581242 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907581242\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.702295, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-633628941 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-633628941\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-97870430 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-97870430\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-335524373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-335524373\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNNZUpPY2czaTZlSTQxdjl2ajJYZFE9PSIsInZhbHVlIjoiYmhyZnlhaTRSNDlJTHpId2hmenJSNENhTW1tUnR3clFtemJOdWxCVVA4REsyeUdEczVsdXJoYVEydmdZY0ZwMlc1VHE0M1lQWmI3K0laZUh3ZFdxeEt6WnhrYllCWHhsNE4wNDRuSUhMRi9MU0dRQ1RXMUQ0K3JGWHJCaHdrS2hYK2lUbmZVYytvVWpyRVdRVzkrRDhsUmZDODE3dXU3a2luL0QzVWpISm9MS1U0NGc0WFlIcmRBU0xTc2Z5aG1YWFMzZDBkSWFJcDZQbm5YdGN0Mjd5K1JxUUJBdDd6TFBPV2IzM1RsYTduTXVxUWtwWXhhU3M3MFNud2FzZkNPZnBSak11SGNpWTFJQ3U3eXoyZGZKcWFhemhlcko0R3BkQXBjWmYxcnhkNTJHSFU2MVVFTURFRFlBU1F3b1JkWEgvMDhuaHZRbTdLMDh6ekZxMU42M3NyZHhlWjlkYWtjUmZRMlduRXlCdzlkZVJvMkZVYWpWZjNiVzVjKzFJbk9STk1qUyttbldta2llR1pkN0FLejNUc2ZaSG95Z2lvc1hUQUZLQWdWL2lVWmx0UzR5NzNUYWtFcGh2NTAwaFlnR2R1RkJrV3A3czg1Ky9GUnRiTVk4UVh1L2ZTcG5ZVHBsWksrL2dVMTFTNmFUS1BIRER0ZWwrVlRxMUlNeThhZXoiLCJtYWMiOiI0NWQwNGI3ZmRkODg1NTkxYmE2ZGRkZGJmZWI4NDBiMzc3ZjhiMWQzNThiODE3ZTE1OGZjOWVmNTEwYzkxNmQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ind6NHdoTmlZYUtOQ1J6Y3VSTDlhbEE9PSIsInZhbHVlIjoiYWhLY3pKTUpNeCtHeVZGRFZBUUFCR3A5L3FYbmhRaFBFZXNjT2hqU1NwSGtvTmh5SlRmblN1dWdnV2JCclZ0THVaMVVoZjJZaWYzZWVqWVdCRmVvRmwxby93anB0L24xK0ZWZ0hlQlFaeFpmK2NjWDRzWElET2UvY2VRK3AxQTFyUnJlaXVGekgwZHF2cHNDWlh3TFBmL0ZNc0gwT2pwWVdIRFZPUzBxTWh0V25PVGUyZTQ2eGFBUVgrWHh6NlZPSktTbk1FUXA3V3lUcG8yQ01xcW9ESCtKSTk5ek5xSW9RWldwT2JVWUs5cWdKaFNYM3FjaGp6ZFVxL3VCQnZya3pVait3eFkxTWNSeElKZkUzYm1FSnFvUk5RbzNrTXpFNXFFR1NtVUpwZmhCSWNiV1EvWUQ0UzNlZy81ZzNVRjhBOTZyTkZyK3J1UmEzdFhiWUhyMDlxN1VYczIvbEVnbkVDQnZnVis5OFdjVXZaQUJHbkFvWndpTkFBa0loTUxNcjJUcURyRlRJclQxTFltY0E2VDVhc2NQRzFZandJQkdMZHlIVU9RQkhpa3YrV1gxSU4zSUVNYzNqYWN6dU9IL1dpQ0k0Mml0WjVRa3ordjh1eE1BVWNpNmlCdjdLeTJXbGd2MDUxOGQ3YThNbmF3TFRnaUJsMno5QmpLc2ZPREkiLCJtYWMiOiJjZTE0YmVmYjE1N2RkMjhkMjNhMjcwMDVjYmM2MWY2YWIxYTQ2NjZhMzU0MTE4YjQ0YjViMmQ4NmQxMzI1MDA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1929115977 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929115977\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-711414608 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:47:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFlT3c4QjNSSHZZOEh4eUg1VEFQcFE9PSIsInZhbHVlIjoiQVUxVVRhUVJna1lybjcrOEFnaDdIYytpQk1keU9OSnN2T284aFpkWEFOaUU2WFVQelFVcklEaEI2T2M0cDFUdEdOQTBnS09acVA4M3hmd3EyTzN5NUZNRWIvcEFXemtsNXJoQTBKQ3c2cXhiMFpKaXh0WUZ3MHZDNSsxaEZWaVczdTlBdENyYmJoQngxemUvNFdDRkJsQytCS2pSY1JVRll5Q0txZkRXM0ZvMkEya0ZYYnJMWkJ4bXN4NFAzeC9LbWV5aDNKMUhTYmxqTk9FNmtJMHhxRnhKRFNkM1lSTkJUUHAxeVJtdTBidmo5bmtVVVkrcWtheFZPK0QvaGlGQ0h6NGF1MUtVVFVBSmY4OW5iVkpGbC9ZRXpIaEd6MG9seXdqYzZFNVZqYmVnQUlRR0VaQWFFWnFvcW5IbS9zNFExNGRNYkpJdXV2MjRncTVjVTREQXlsaEhMOVdTREZuaHpPV2dVZThCdHlZUFEyQytidU9sVDNWc2s0UUlJWFZkeFhQcWlZOWhmN3dHelBOWmVFRm1DTUZHN3dOOEhrMTYvVkRrYUl3Tnd2bUJvL0RjL2l6dmVkNmJiMDBLZTU0MVRUdlJkcit6UHJHQ3NDdy9DVlBGZXJJRTIwbXI5LzVZR2QvMm12TnBvV3diaVNKOXMzRm5HNktwSjR2dWltT3oiLCJtYWMiOiI2YjZmYjIyM2YyZjI3YmNjZDJkMjViMDBiM2Y4YTVlODgwMjJlNzc2NWFlMTgwMDdjZGE4ZDQ1YTQ2YjlkOTAzIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFzY1dPWTZvTHRZdjBoYjJUTVNpZEE9PSIsInZhbHVlIjoibUk5OEFzdTFiTHJ2VmFTZExmWFY1UHdaaEtjZ2hXY3o2aTJHQkJyMTJoUG9SYnNtdmk4bW9SMXNwVlo0NTYrNkozZXFnaDhTWG1mVWt3VkIxZ254MUp2RExuQ0p1bGhCdjJkc0NIaUI3c2FuY3dZeldUbHpTQjRzVGdHUEMxQ3FmeWRtQWs4UFRkR2RZanJ6MkowVUg4d0ljWkk3VUptTU1IWG44UzY5TytUWXR2WWJIL080VjN4eFk1NWxKUDF2SlZaa1V5cU1YOFE0a0J5VTdVZ25ObDB3azczMExxRkQ0Z2hGakhnQmlxUUJTNGtpUE1ZcGdnN0tuSDdOVFNna01VZUtkcEFmdW5JSnBxakpZeTVTVnRYVDNMdlRaT3dMbFRTNnFvVWszTkU3RVZxcnJlMjZVd1hrL1M1WHF3THZXVm9jditGelFISitCblkzakFFR1p4WkJzcE1rR2xINFZCdG5VWWQ1MWlqTDk5NFkyMmg5SW1tdDhYMFVITzNMbWx1ZlFucm9RclNOMTlQQkltOWZHZnpkc245K3lacktlQVBORmpEZ3ZBVk0wQk9OZmNUcTZqY2lMNUI2d0o0Q3lLNGtBRDc5RXRNK2RRT0lhaDIzaWZNQ29Lem1mMVdqcFBpZ1ZzT2RNZjh2cjR1cWFtaG9pQTlGcEhKdG95THoiLCJtYWMiOiJlYTVmZjZiYzFkYTNhZjFkNjJhZjRkOTZkOTg0NDY5ZDE3Yjg5Yjk5YmE5ODE0ZmI1YTYxYWFiMTkyOGNiMmQyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFlT3c4QjNSSHZZOEh4eUg1VEFQcFE9PSIsInZhbHVlIjoiQVUxVVRhUVJna1lybjcrOEFnaDdIYytpQk1keU9OSnN2T284aFpkWEFOaUU2WFVQelFVcklEaEI2T2M0cDFUdEdOQTBnS09acVA4M3hmd3EyTzN5NUZNRWIvcEFXemtsNXJoQTBKQ3c2cXhiMFpKaXh0WUZ3MHZDNSsxaEZWaVczdTlBdENyYmJoQngxemUvNFdDRkJsQytCS2pSY1JVRll5Q0txZkRXM0ZvMkEya0ZYYnJMWkJ4bXN4NFAzeC9LbWV5aDNKMUhTYmxqTk9FNmtJMHhxRnhKRFNkM1lSTkJUUHAxeVJtdTBidmo5bmtVVVkrcWtheFZPK0QvaGlGQ0h6NGF1MUtVVFVBSmY4OW5iVkpGbC9ZRXpIaEd6MG9seXdqYzZFNVZqYmVnQUlRR0VaQWFFWnFvcW5IbS9zNFExNGRNYkpJdXV2MjRncTVjVTREQXlsaEhMOVdTREZuaHpPV2dVZThCdHlZUFEyQytidU9sVDNWc2s0UUlJWFZkeFhQcWlZOWhmN3dHelBOWmVFRm1DTUZHN3dOOEhrMTYvVkRrYUl3Tnd2bUJvL0RjL2l6dmVkNmJiMDBLZTU0MVRUdlJkcit6UHJHQ3NDdy9DVlBGZXJJRTIwbXI5LzVZR2QvMm12TnBvV3diaVNKOXMzRm5HNktwSjR2dWltT3oiLCJtYWMiOiI2YjZmYjIyM2YyZjI3YmNjZDJkMjViMDBiM2Y4YTVlODgwMjJlNzc2NWFlMTgwMDdjZGE4ZDQ1YTQ2YjlkOTAzIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFzY1dPWTZvTHRZdjBoYjJUTVNpZEE9PSIsInZhbHVlIjoibUk5OEFzdTFiTHJ2VmFTZExmWFY1UHdaaEtjZ2hXY3o2aTJHQkJyMTJoUG9SYnNtdmk4bW9SMXNwVlo0NTYrNkozZXFnaDhTWG1mVWt3VkIxZ254MUp2RExuQ0p1bGhCdjJkc0NIaUI3c2FuY3dZeldUbHpTQjRzVGdHUEMxQ3FmeWRtQWs4UFRkR2RZanJ6MkowVUg4d0ljWkk3VUptTU1IWG44UzY5TytUWXR2WWJIL080VjN4eFk1NWxKUDF2SlZaa1V5cU1YOFE0a0J5VTdVZ25ObDB3azczMExxRkQ0Z2hGakhnQmlxUUJTNGtpUE1ZcGdnN0tuSDdOVFNna01VZUtkcEFmdW5JSnBxakpZeTVTVnRYVDNMdlRaT3dMbFRTNnFvVWszTkU3RVZxcnJlMjZVd1hrL1M1WHF3THZXVm9jditGelFISitCblkzakFFR1p4WkJzcE1rR2xINFZCdG5VWWQ1MWlqTDk5NFkyMmg5SW1tdDhYMFVITzNMbWx1ZlFucm9RclNOMTlQQkltOWZHZnpkc245K3lacktlQVBORmpEZ3ZBVk0wQk9OZmNUcTZqY2lMNUI2d0o0Q3lLNGtBRDc5RXRNK2RRT0lhaDIzaWZNQ29Lem1mMVdqcFBpZ1ZzT2RNZjh2cjR1cWFtaG9pQTlGcEhKdG95THoiLCJtYWMiOiJlYTVmZjZiYzFkYTNhZjFkNjJhZjRkOTZkOTg0NDY5ZDE3Yjg5Yjk5YmE5ODE0ZmI1YTYxYWFiMTkyOGNiMmQyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711414608\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1679784299 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679784299\", {\"maxDepth\":0})</script>\n"}}