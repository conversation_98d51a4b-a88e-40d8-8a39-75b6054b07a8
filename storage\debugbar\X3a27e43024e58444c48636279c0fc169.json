{"__meta": {"id": "X3a27e43024e58444c48636279c0fc169", "datetime": "2025-07-12 16:07:13", "utime": 1752336433.198428, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.554735, "end": 1752336433.198448, "duration": 0.6437129974365234, "duration_str": "644ms", "measures": [{"label": "Booting", "start": **********.554735, "relative_start": 0, "end": **********.888878, "relative_end": **********.888878, "duration": 0.33414316177368164, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.888887, "relative_start": 0.3341519832611084, "end": 1752336433.198451, "relative_end": 3.0994415283203125e-06, "duration": 0.30956411361694336, "duration_str": "310ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51498024, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00252, "accumulated_duration_str": "2.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 70}], "start": **********.927865, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.159}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.932983, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.159, "width_percent": 19.841}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Ikx2ekhhcWVvYTNaSWkwV2dVWS9pTnc9PSIsInZhbHVlIjoidkhTR2RybExGMERhanFaaUVHYXBXdz09IiwibWFjIjoiYWJjNzdjMzZjMjg3NTgyYjY5YmZiYjYyZTk3YTU5NTQyYjkyMTRhZmI2YzM0MjBkODA3Y2NmYjQwZDlmNmQ5NiIsInRhZyI6IiJ9\"\n]", "pos": "array:1 [\n  2421 => array:9 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"2421\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1725331515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725331515\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-394387308 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1eejy7q%7C1752336416431%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBxZDFWdHhmbU15UTM2aHdYQ0tUMWc9PSIsInZhbHVlIjoiaThYbnJ1bnltRExqWWJFVTQ5d3lSeWpsMUM1VTdvMHd5elJPbTdzcW9PZEdiNWlKRndmWWduOUdaZzkwWmphdXF4OEwzSEVXckxTQVU3dFZlb1dEenpLMkE5WmNObXhIOGVOTnRzNGg1OHhTS1lKczR3dERsRkFuTXJjZnNhajdBcW42bzQ4bmphby9VZlJoZnQzNWhjazd5aXd4YTFtYkE3dGxXZUo5OGJMTU9rWDlZZ0QvRUlnUUZNUGgyeDBMb0xzWFd1RkxwQ0hvMG1WRytRTDhwbC9NZjNpeWVSS3ZXd1hobHloOEFIb25UYTVOOXN4OHVQai9EMFpXOWdlbVRZVUZXc0pyOGpwdDdpSkxpVURQUUtvbmxOZi8vTzIyMUJhdmt1UGtuNHYzQ2xBeTJ1WGhYT1E4OCtTTWhpVU9xSEZDVmdhYVlrTWNDa3BkUlZBZSs0eGQza2pyUU55Nmd6QjVMZ1RpVVFRWGlXUEpiRmZjZ3M3VFpib1ErcGJoSHcxdTZIWllNb09IK25zdlV4K29VZ2lUWUdFeEVjUTEwbElleThXR2QwdXRkYm9mRURSMkp2aGhxeHZlRFpOWjl2NDFWbnFyVE1GdXRLU3dmZStCYnFKYjlaVVlMeElEaFRkOXJsdnQ4UEhCcVJwcmxXUzh2Mk9CUnNDWUJ3ekUiLCJtYWMiOiI0YmExZDhiOGY3MmQyZGI5NmQxNTg1ZGU4MjdhMmE3Y2MyMjlkNTc5NDE0OTA5MWFhMTVlNGUyNTk2ZTc5MGNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1Fd2hZUHR3Um9YbW9BRk5ad1JQZ1E9PSIsInZhbHVlIjoiTWI5S0NGdjJHS0wveEYrcmdoNU1RdFNnclJtZHRWVUZ2dWt3c2VIWU9ya0d4N25BUTNyMFcrYjNDZmo2S3hQUjBBZWtkOXlZN29rMXhqbWdkenZNL1BBT3k3RDB4eFg1ZFhlYURJMUlOVHB4b1dpNU9OVTl6WVhTWHBQcDlMR212anJRZEFSVnhmbTA2M2RLeGRMaG82cUNTSkF5aElmSFA0U0N2SUNsZDdOMzBNa3VSaWJKZnU2T2ZiTjZhZDRUSEt1TVlKbi92VU9QaDJ2VEt0WUZzZ2xMMmVmMDJCVlNmY29TV0dId3VRWUtlZlhqWGxJWmNqQ3RSUVozVmFQUFg1dHVvODd1a3ZRbEtDRmd6enk4V3R4dFQvcW5CTXlMd3B3UlNENDBZZzB2WGNhTXRsT3BXVDhXK3dtVWR0eEdtN3d2QmxQQmtkYkN6ZXVTMWpQM05IbmsxR2d3UFVxUVhDa0E1OTYwczBSc0tYckIxZmFvd0JsMSsrb0FwQlNlUVZ0b29vMGFhZ2JuVjArODMxek9iR2hLV2xoMVRIVGo4WWJNUjE5N2ZXQUxtL2Ezam93Wm5aMEhCZlZHeEhvY203aDlzMXZXNGJTYWEzY2hXelJFby9GREZCQ3l4eXFNVDNReWZzeHZVWlk1SDloTHdELzZSRkJYVG1PYVhrWEgiLCJtYWMiOiJiNDk1MTc4ZTViYWU1ZWE5NjE0ODNiMDY1N2Q0NTQzYjcwYjU5MTA3MGQzNzdhNjUyY2I5YjU0ODdkODUxYmZjIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394387308\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1862684112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862684112\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-931329270 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:07:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJXK045R0xxL2IvNytuTlBFbnAzeHc9PSIsInZhbHVlIjoiRVN3WVBUbDMrQTNmdHk2WjZ2R2c2bXl6K2FCaHhWbDZyb3pjVk8vcnFpcnE4NXNFdHU0RHJSK25Dek1TVlNvdjI4bmJJNDdnSWxEV0twQk1WSnM5MEFrckNDOHhYYkNOTzBXSkExaHlTaWwwVUJQanlkQ3Q5a2xWQ3Z0bzZHdUlVUzJvNHlOc2crMVBxYmQrUG8wZFpnZ0QzazRDU3pLc1BOejgwNXZBbWdiUXI0OFBidEtVLzhKcTliRkphS0xGN3VJYzNzTUo2Q3Zya2FVT05pVGN5a0I4VTZDK1FmUnIwSG00NjBrNE1ibVQ0OE9BQVpOKzZCUnBvRDBmVTZOOEhGa1J1MWM0TGxjREU1UDQ4MEJBc0pRZXdFMGhBc1BVdFEreERhWGlYdHpVVjlWdTdtUVF1SHlQME44cHVMNENHbjNWV0Z0WmhIdVJQTTZoWkR6eVdna2tZcUxtVTlzOS9oZWtWWk9PSWFUL0grYlRxT29IdW1Zb0F6WUNPb2tLZngyTDIxY1Fod0oxTWNHbkdPanlUb0cvZ2o2SFdGdm1sRlNiTHU0TFRld3lEYXhuQ0dTd0F0OHFkakhQaEhZZ3ZKcFlHeGNkak12cDRnNVRWb0Y3YXBUd1lIVXNiRHAwVVliNldOdVhGNlNoMStCZjdmZVZJdjFKdnR0bVprcDIiLCJtYWMiOiJkMmIyNmRkYmM2NTgyZWM4YjgxOTkwMzk5M2Y3ZmExNGZhNjkxMWFiOWFjMGZkZjBjODU1YTIwOWE5MmIxYTBlIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:07:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBDd1Q0aVBqaE1JQXNvOHdNYXZsSlE9PSIsInZhbHVlIjoiUlJBa1pMMlg5Ym5LUnBSMVFwalBGSlUvZGdOV1N2MzJQRUJkdm8weW9WUnlVYUdCRFdUa0MxMXVTMjJTSVJqS20rL1JzaTlndVpoandKSzAzanlVU1Q4YlZsUFYwVnAyWlRXOGhTVUxLVjZBTkZFcFF6NFZhdEpUMkZNby82YkxXd2JFTCtNaGVGYTVuK1d4TUU0aUh3aUptNlBHY0tzZmk3cmMyUzVEVko1TUVaSXJQMi9yWnltRVF4MU5CVXdOSDE4NVRHeDllMTBBeUJPbDlXeFBPZWtEeTJYOHEwSkFKci82Wm5wZFFOaUJzTTVGWUl3ZXAwSU13Y3pXd0pydFBLK3p2bjdBTUxJVXF0NmNCYlRDZFdMRDBDcFd1TEhhU1pwaWtvTkFXTDF3dTdGM3F1TUZuNmZZcjdTMElhb0JsbkYyRUt1MnNnVExWeVFtQmswTytuQktHZG5HVFN3T2FmMUFoYmVyZjlHNE42ZzdaVU11ZlJoL0FXYUlDSTJrbkRlQmQyd015Z2ljV0tOb3hjbklaMkRNZFJJY3BZZ2d2UGdDWjdDRFNwRmJLem14UG1QaUI3c0lLdG10TWU4Y2xUSmZzUVRkUzRvSTdwNW9jL3lhMTlsMW5yTzZiYUUzM2g3anFPK25DWnVwazkwamN1cHh1WVdqUVFzWVFVbVAiLCJtYWMiOiI4Y2U5MzRhYjNjMjU0NDUyN2I1YTlkYzI5MTZiNDA1YzBhNzgyYmEyYWRmMmRjZDE0OGE5N2JjMDY2YzI5MjZjIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:07:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJXK045R0xxL2IvNytuTlBFbnAzeHc9PSIsInZhbHVlIjoiRVN3WVBUbDMrQTNmdHk2WjZ2R2c2bXl6K2FCaHhWbDZyb3pjVk8vcnFpcnE4NXNFdHU0RHJSK25Dek1TVlNvdjI4bmJJNDdnSWxEV0twQk1WSnM5MEFrckNDOHhYYkNOTzBXSkExaHlTaWwwVUJQanlkQ3Q5a2xWQ3Z0bzZHdUlVUzJvNHlOc2crMVBxYmQrUG8wZFpnZ0QzazRDU3pLc1BOejgwNXZBbWdiUXI0OFBidEtVLzhKcTliRkphS0xGN3VJYzNzTUo2Q3Zya2FVT05pVGN5a0I4VTZDK1FmUnIwSG00NjBrNE1ibVQ0OE9BQVpOKzZCUnBvRDBmVTZOOEhGa1J1MWM0TGxjREU1UDQ4MEJBc0pRZXdFMGhBc1BVdFEreERhWGlYdHpVVjlWdTdtUVF1SHlQME44cHVMNENHbjNWV0Z0WmhIdVJQTTZoWkR6eVdna2tZcUxtVTlzOS9oZWtWWk9PSWFUL0grYlRxT29IdW1Zb0F6WUNPb2tLZngyTDIxY1Fod0oxTWNHbkdPanlUb0cvZ2o2SFdGdm1sRlNiTHU0TFRld3lEYXhuQ0dTd0F0OHFkakhQaEhZZ3ZKcFlHeGNkak12cDRnNVRWb0Y3YXBUd1lIVXNiRHAwVVliNldOdVhGNlNoMStCZjdmZVZJdjFKdnR0bVprcDIiLCJtYWMiOiJkMmIyNmRkYmM2NTgyZWM4YjgxOTkwMzk5M2Y3ZmExNGZhNjkxMWFiOWFjMGZkZjBjODU1YTIwOWE5MmIxYTBlIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:07:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBDd1Q0aVBqaE1JQXNvOHdNYXZsSlE9PSIsInZhbHVlIjoiUlJBa1pMMlg5Ym5LUnBSMVFwalBGSlUvZGdOV1N2MzJQRUJkdm8weW9WUnlVYUdCRFdUa0MxMXVTMjJTSVJqS20rL1JzaTlndVpoandKSzAzanlVU1Q4YlZsUFYwVnAyWlRXOGhTVUxLVjZBTkZFcFF6NFZhdEpUMkZNby82YkxXd2JFTCtNaGVGYTVuK1d4TUU0aUh3aUptNlBHY0tzZmk3cmMyUzVEVko1TUVaSXJQMi9yWnltRVF4MU5CVXdOSDE4NVRHeDllMTBBeUJPbDlXeFBPZWtEeTJYOHEwSkFKci82Wm5wZFFOaUJzTTVGWUl3ZXAwSU13Y3pXd0pydFBLK3p2bjdBTUxJVXF0NmNCYlRDZFdMRDBDcFd1TEhhU1pwaWtvTkFXTDF3dTdGM3F1TUZuNmZZcjdTMElhb0JsbkYyRUt1MnNnVExWeVFtQmswTytuQktHZG5HVFN3T2FmMUFoYmVyZjlHNE42ZzdaVU11ZlJoL0FXYUlDSTJrbkRlQmQyd015Z2ljV0tOb3hjbklaMkRNZFJJY3BZZ2d2UGdDWjdDRFNwRmJLem14UG1QaUI3c0lLdG10TWU4Y2xUSmZzUVRkUzRvSTdwNW9jL3lhMTlsMW5yTzZiYUUzM2g3anFPK25DWnVwazkwamN1cHh1WVdqUVFzWVFVbVAiLCJtYWMiOiI4Y2U5MzRhYjNjMjU0NDUyN2I1YTlkYzI5MTZiNDA1YzBhNzgyYmEyYWRmMmRjZDE0OGE5N2JjMDY2YzI5MjZjIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:07:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931329270\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ikx2ekhhcWVvYTNaSWkwV2dVWS9pTnc9PSIsInZhbHVlIjoidkhTR2RybExGMERhanFaaUVHYXBXdz09IiwibWFjIjoiYWJjNzdjMzZjMjg3NTgyYjY5YmZiYjYyZTk3YTU5NTQyYjkyMTRhZmI2YzM0MjBkODA3Y2NmYjQwZDlmNmQ5NiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}