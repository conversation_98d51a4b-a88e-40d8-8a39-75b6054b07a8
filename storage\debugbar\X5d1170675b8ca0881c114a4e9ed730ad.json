{"__meta": {"id": "X5d1170675b8ca0881c114a4e9ed730ad", "datetime": "2025-07-12 16:06:09", "utime": **********.077223, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752336368.603358, "end": **********.077247, "duration": 0.4738888740539551, "duration_str": "474ms", "measures": [{"label": "Booting", "start": 1752336368.603358, "relative_start": 0, "end": 1752336368.991678, "relative_end": 1752336368.991678, "duration": 0.3883199691772461, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752336368.991685, "relative_start": 0.38832688331604004, "end": **********.077249, "relative_end": 2.1457672119140625e-06, "duration": 0.08556413650512695, "duration_str": "85.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45708880, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02527, "accumulated_duration_str": "25.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.020459, "duration": 0.02402, "duration_str": "24.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.053}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.05337, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.053, "width_percent": 2.216}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.059956, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.269, "width_percent": 2.731}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "pos": "array:1 [\n  2421 => array:9 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"2421\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1204729624 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204729624\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1760554496 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktEa2sxaS94YXFKYTZoVmRFaHk0eFE9PSIsInZhbHVlIjoiOU5GekppeEJhbndFY0gyUElRQWpyczMvNkU3cUhKVlV1MzRMbWgxSFA4Zm5MME90dVlsNEtVaHZGTm13Vzh4bktPTEIwSzFGbWlYOVNmVXFRZysxdmdqOWFNcXltOXQ2SURSYXhFM3BPMXhQVmM5ajdEdXVPYWEwOWh2ZXZGckRuSXlkOGhtc0NLalJPa3Z4MnZrVUFIVUxwNDlKN0RmbndYNXlTaEh5b0JKdVFQRy9LK2R4OTM2Z0VQb2NXWFVJWmExRlNsWGpBOUI3SktOSXkwNUtBS1NZcjkybUdackNlS3ZiWVN5MUYxWWprbExyUXRacFhBT25veEpxbDNTdjRxR1BiZGdSNTM4cUxxZ3NjYlJ0ZWRONzZxKzAveVpaUlU3Y25Ia2t1VHJ5dm1LRCtIUmxoYlB3azdybVZNZ25KRDJwTWlYQVBDV3FFckRJOXFodGlqOVhMQS82THhKaml5bmg0L09WSEVmeVBkU2Q2K1pjZW9Wblk4bWF4VDlFaDZMVXhKUythM1JEUFZRZDd1V01uVjZDSy9KeUNUbVhNTUZTVzJoc1orUVBYUDdVUW5oMjU2RTU1c3FiWTdZYXFGcnBwbmk2Q3ZhR1ZnUk50SkluNS9ISzB4QSttcW55bG94c0hYTisrNUhqci9lSy9RMjlYMEtZZmkvZ0Qwa3IiLCJtYWMiOiIxMGFiY2QyMDlmYzkzNzljMWJlM2RlNGFjYmY4NGUwNmE1YWIxNTg4MjYwZDJjY2QwOTJlMTE0OWRkMDY4ZTdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkF3MWFCM29GRkhlYjF2dllIejczS3c9PSIsInZhbHVlIjoiMEpKYVBoR0pBZnUraTlIWFhwZmgyVXhmSzZQa2pwdWVHbmtVNldvSWlTZmxHTERnYlN1UXM4NVJMRWw5NmZDSGZVQm1pRkRhOHNBaFlIb055V2lBNnM0S3RRTSsvNkV6dFBJWXRtTVNzNDI0enVqTUNXakltOHlrVG5FbWZvM2QxenBCcWE5L0RiUEkra3VyZ1hHUW9sdTZDb1U4cGFzTC9BTzE2YkJuaUdaTlpXcGpxVDhTc3ovZWpHNFdKY0RKWjE5Y3g1NDFxN0ViU0pueDNyUU4wcWZXV3psOU02Z1Bjcis3OGk4S2V6cTd4dzRrelpUallCaGpMakxaRVcxMmdyTGhKOTdCQnhHSm5qeDM5ZUVlSGZ6cUpIbHJIYjFFUmRjRzF5WDM5Nm1QRmhFaytzYzVMRm9CUUtrbzh3dTUwbEwwcGFqWGVxbmhCWEhxZHNiY2NmUS91V0ZJUVVVZUZPREkwY2tZd0JnZ1BGYWdNbkZZbERJdEVQUUhZb2JIaitYOGtwMkdSeXNsSk1pN2Rxa3Z2MjAvL3hKV0ozV3V6OVpzWHA0VGtQL2Q5T2VQMDltRzdveW0wK0hMNjI1aFo4UlJWalZGZEN1eDVPdHc2QVVqcXlOV2Q0ZVdTdXE2Yy81eWZUNUxBL1dESU1JQUIvZlNFVFZaanlucUFkaHciLCJtYWMiOiI1MGM4N2FhNTJjYjZjOTQyNTIwNzViYjkyYjIzMDc0OTc5NjRhZTNmMzZiOTQwOTdhNGYyZDZiOTc1MjUyYjdkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760554496\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-466470603 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466470603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-835618266 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:06:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5NZUNDS1Z1ekg3aEViN3A4Q1pIWVE9PSIsInZhbHVlIjoiREVIQklXYURuWDFpZ0M0RU1jWUdtVW9MQ05FQXNTTmNMSjBzU0hkL3ZpZkxXMHBVeGZDUk1IZS9JTGV5SHBUVENBR05JdWtRV2JMVEl0YmJDU084dE9McTNJZkNCcHVucU5kOUZLUnhPT055aDdtc3Zzekc1NlNmYksvalAzZGoyK2xUdlppZ1ZHNFpLeDV3Mm1JcmJzcXh6RW5XV01jVks1NFNaNk9JVmc1NHZkRDVaUTArbURLdnRtMGRpU042VkhuZXJPMVU5VmJkSnI2Q0t6c3RNK1J5ZjlPRk82UmppdW51cDl4dzhuRWtpQUR5dFpFSjZ1bXpUdVBVMjA4NnBCTnE0VkJzZGxkOWVDQnVkQWhZRlR0cWhaTXoyaWtvdnNPMGI4N0crM3NseVZrVlJIQTA1QjlBNjhsY0tTelQwem1yTlZGSWpDTVRjRGhBZDBrM3R2b1F0b0JGVDV2UXpORUYyNFpITEJpYVo0MCtvamFUNDNkR2NmUGlaOWdobk53WU1xWnY3Q0NRWEV6Q2Jzb21Sb20xNC96ckZoQVJvbytXZmNxeVlEVXE0dTB2b0h4RFpxaUUvUko5WTBHQmZjTjlrUHhsUUxzc1lXbmlqWkpxblhNN2c1WUJXZ2J6TndHKzFObG5LSU1GNEYvekRhUTcvSXJCM2ltZFhXS1IiLCJtYWMiOiJkNTk0MTM5N2M4YzVkNWE2ZjE3ZjAzMzE0YmM0MWJlYjY4MWZhYWUzYTYzOTVlODIxMmYwMWFmNGU0ZjcyOTA0IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklOR3JKSTlKeTZ1RnVpT1BaK3BDblE9PSIsInZhbHVlIjoiMkozMFBvaXhNRVJOdjFZS3NxNzBnT1FGVUM1ay9Tekg5QkJzV2wxSDl0WmJVYnJEeWtqWmt5Q2VQUDFiN3JmUDlvTlNGTm0xM2NudW1LY25vQ0RFRmN4bllQazZjY1NBQVZ5Rzg0SnFjdFk2MDF2eFhTdWRUKzBCaDJjczZhSlA3TnJIc1VqWWF3QXV1NllIV01WWXpBVFpGUGpoWTRKVXEzZzRmcFpWalBBOEM1QWtXc1V1TkRuR1FkK2VmYWhpT1Z4YU4vbFhkL0VUR2FMWU5ZU3NsaDlJR0ZOM0dGbFBTdmUyZEdNUmJjbisrMWtXYmZlSFlUQ1hJUUFXRDE3VmVyVTlVbVovN2p5VzR5TmZxN0ljVVRid2NoOXhVVGIyQ0tNYUY2aUZQZVpYV0dkakNpMHErSzlXME54S200bVRqZEhaeWNuYlpEejhjYU10SUgya0lqZ0wwQ050NU5KWTRkZE4waUFpMmVsdTd4M2xOQ0k1dk0vV21HbW85VU81b2hBNENUbTgxTk9qdTAzSTY5UzRuZUoyVHpiMUVqQStNcHVINlFJaHZubzh3c2J6UjVpYnYvd2l2akZLVzN0RDBFYStKMzNrRFJsemRvbkprVW9GL2l2TWFtNFI4aW9kUWNoZkozSTRiTzVsWEcveCtnZ1grRFpWU29vNlltL3oiLCJtYWMiOiI0NTFjMDc4M2IxZTdhOTU2NGQzNDlhMTM1NTg4NzAxOTRkMmJiM2YyY2VlZWJhYmEwZjE1YmJiMzVlYWUwNjlhIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5NZUNDS1Z1ekg3aEViN3A4Q1pIWVE9PSIsInZhbHVlIjoiREVIQklXYURuWDFpZ0M0RU1jWUdtVW9MQ05FQXNTTmNMSjBzU0hkL3ZpZkxXMHBVeGZDUk1IZS9JTGV5SHBUVENBR05JdWtRV2JMVEl0YmJDU084dE9McTNJZkNCcHVucU5kOUZLUnhPT055aDdtc3Zzekc1NlNmYksvalAzZGoyK2xUdlppZ1ZHNFpLeDV3Mm1JcmJzcXh6RW5XV01jVks1NFNaNk9JVmc1NHZkRDVaUTArbURLdnRtMGRpU042VkhuZXJPMVU5VmJkSnI2Q0t6c3RNK1J5ZjlPRk82UmppdW51cDl4dzhuRWtpQUR5dFpFSjZ1bXpUdVBVMjA4NnBCTnE0VkJzZGxkOWVDQnVkQWhZRlR0cWhaTXoyaWtvdnNPMGI4N0crM3NseVZrVlJIQTA1QjlBNjhsY0tTelQwem1yTlZGSWpDTVRjRGhBZDBrM3R2b1F0b0JGVDV2UXpORUYyNFpITEJpYVo0MCtvamFUNDNkR2NmUGlaOWdobk53WU1xWnY3Q0NRWEV6Q2Jzb21Sb20xNC96ckZoQVJvbytXZmNxeVlEVXE0dTB2b0h4RFpxaUUvUko5WTBHQmZjTjlrUHhsUUxzc1lXbmlqWkpxblhNN2c1WUJXZ2J6TndHKzFObG5LSU1GNEYvekRhUTcvSXJCM2ltZFhXS1IiLCJtYWMiOiJkNTk0MTM5N2M4YzVkNWE2ZjE3ZjAzMzE0YmM0MWJlYjY4MWZhYWUzYTYzOTVlODIxMmYwMWFmNGU0ZjcyOTA0IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklOR3JKSTlKeTZ1RnVpT1BaK3BDblE9PSIsInZhbHVlIjoiMkozMFBvaXhNRVJOdjFZS3NxNzBnT1FGVUM1ay9Tekg5QkJzV2wxSDl0WmJVYnJEeWtqWmt5Q2VQUDFiN3JmUDlvTlNGTm0xM2NudW1LY25vQ0RFRmN4bllQazZjY1NBQVZ5Rzg0SnFjdFk2MDF2eFhTdWRUKzBCaDJjczZhSlA3TnJIc1VqWWF3QXV1NllIV01WWXpBVFpGUGpoWTRKVXEzZzRmcFpWalBBOEM1QWtXc1V1TkRuR1FkK2VmYWhpT1Z4YU4vbFhkL0VUR2FMWU5ZU3NsaDlJR0ZOM0dGbFBTdmUyZEdNUmJjbisrMWtXYmZlSFlUQ1hJUUFXRDE3VmVyVTlVbVovN2p5VzR5TmZxN0ljVVRid2NoOXhVVGIyQ0tNYUY2aUZQZVpYV0dkakNpMHErSzlXME54S200bVRqZEhaeWNuYlpEejhjYU10SUgya0lqZ0wwQ050NU5KWTRkZE4waUFpMmVsdTd4M2xOQ0k1dk0vV21HbW85VU81b2hBNENUbTgxTk9qdTAzSTY5UzRuZUoyVHpiMUVqQStNcHVINlFJaHZubzh3c2J6UjVpYnYvd2l2akZLVzN0RDBFYStKMzNrRFJsemRvbkprVW9GL2l2TWFtNFI4aW9kUWNoZkozSTRiTzVsWEcveCtnZ1grRFpWU29vNlltL3oiLCJtYWMiOiI0NTFjMDc4M2IxZTdhOTU2NGQzNDlhMTM1NTg4NzAxOTRkMmJiM2YyY2VlZWJhYmEwZjE1YmJiMzVlYWUwNjlhIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835618266\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}