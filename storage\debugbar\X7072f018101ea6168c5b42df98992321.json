{"__meta": {"id": "X7072f018101ea6168c5b42df98992321", "datetime": "2025-07-12 15:47:25", "utime": **********.577572, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.130455, "end": **********.577585, "duration": 0.4471299648284912, "duration_str": "447ms", "measures": [{"label": "Booting", "start": **********.130455, "relative_start": 0, "end": **********.530468, "relative_end": **********.530468, "duration": 0.4000129699707031, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.530478, "relative_start": 0.4000229835510254, "end": **********.577587, "relative_end": 1.9073486328125e-06, "duration": 0.04710888862609863, "duration_str": "47.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43871680, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00213, "accumulated_duration_str": "2.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.564035, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.385}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.569724, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 86.385, "width_percent": 13.615}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-660420076 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-660420076\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1442919664 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442919664\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1250743316 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1250743316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1735604362 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZqUDk5dzQ1TllXSGdBemgyeTdGa0E9PSIsInZhbHVlIjoiN2dCd3hCaTl6YzRQSDlvNDduMm9PSVVoU0hOVUJMcmFPKzBOVVFqTnBBZFhDWE9iRFZxSHdSV0hRcTNQaG9NeFhZTGpXbnM0RVFtbTZMUU9Hb3RncGpGYWJTRld6ZFF4REIvYUt4bThBVHg2cUtHaWdYVTJQTU9qdmtHdEFDNEw1WVZ3dUV3TzV2eVdydDJGbElwSGlkKzd4c3F6N1VUMEFSS0c4UXQxUHJtYm5zYnNKZUsxOThxMDdnd2lnZzRuUzZtU1g4aXRCYVUvUElrcmJkemNublFUR1JnYWhnWkVTSCtSdG5QMkVMei9WOUV0Vnh6VHVBbGlvTXdXVytCd0U3enN6bEtrc1ZLU1FMNkdHellKMU1RRUxIek56MGlLMzhwcW4yNmlmNlFWeHNwaXBpWHZjdEVwdHFjYS9paldiTnV5YVVidjMrVWtQZWVEYXl2R29Qak1tanF2bmcyekZCY1N5T29YVDdDbm5qUDc1MkRGREF3TnBmZUVaYTBHR3lBcFNWMTVlVkpEQVpnZkk5VVhaeTBoUUFad0xNZFJNRmlrT0hOc3V3bytkYkRteTJlUzRSRVFEWVJCOXhTNy8rYWM5K2dhQmlmOHdCNW4wSHg2bVl5aDdudmplVjhQeU96UEh4KzFmOHorVW8rWEdrYW9hcU04R3hsTXV5UXAiLCJtYWMiOiJiMzNlNzM0YWE2ZTM0MDI0NDVkODMxNjE3YjI2YThlNzg1YWYyNGFmZTNiOGZlZmFjNGVmZWFiYWQ0ZjU0MjcyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpkR0M3Qk53bmR4S0syUHVXVVFHTmc9PSIsInZhbHVlIjoiY1ZWSDB1Tmd3b0ExdngwSU5MbXRYL0RMa2E4UzdGUHlpYUtpVjBlVDJtanJrNmM4V0h3a2djcmNvZkhGRHFaSm9DOXkzVHhjUG5VV0g0VWovS09OSFprYlJnOWZJdGJ0YVJrc0Vxd2NUWXd2WXFEM0lncDdYRDZGWUNOb29HNDBVcmhhN0FjZFpUWTF4WHFIVzVtcGFMMXlYVzF5SlZVY3RIbVBoRlhNeFRER3lFbkVySkRRVUFPbUcxZTVEQ0tLRGhNUFQxUmU0NjdqM3ZDZkxmcmZOYWd0WnRhMDN4Ti9BSDZ0dFFLeDZ1aFZlTUJHODJQbVRGVDBJeW1yY0hlRnpsOE9CZHBzRUZwQ05uY0J2TUxCenBFS2w4UlRJTDBkaXJkWitrTXA3aWYwbEVuTGxzK2FGNHRuOXA0L3hsVmNpWWNSWStjTmZHZjk0WjY4bHNJNjBUYWNpQnRtK2RSSFNkbjdJMzVKSGkxYXE2M1hjMEw3VHRUYUh6cVdtc01JQXVwU3VPY2Jwclh4b3U2QzkxTEtjL1JYQ1QrM3d6MTlGcXR0MjBUSGd2bFJJSG85VmpRaXMwNDBRMVB3RjlUb2ZUZEZPTWNheStzdDM4L1pGS1Uwd2VyU0drc1BuYjVzUkUzUlEzdzVxWlRPakhoamh0bS9ONDFPRHRDM3ZrNHIiLCJtYWMiOiI1N2Q5YzNhNTc1MWU2ZjBlZTI0MmZmZWUwYWE1OGFlNGUwM2ZiYmQyZmRlMzhmYzAyMGEwNjAwNTBmMDBjZjMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735604362\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-759569693 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759569693\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1495931984 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:47:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllQRHM2VnRYMkR5OU9WR1orS3p3dnc9PSIsInZhbHVlIjoiaGx6RklOSHRQQXN6ODN2ekpWcFlJdWVHVGRpeWpYT1RLYnlKMjh5c2M2VW5INlZjQXBMRlA2bU54SUMvaisweU42a29tSndCalA4MFMrdXhSSHAyZVBDcmZTN1pWY3ZXNU1pUUdxRS9wU3cxaGM5YTlRc3NNcnFBVFNPSHRoQW1jZDNWRm5EVmlQZW5IblpPV3BOT01yUW1JMzQwVDR6Nlk5eVBzOTNuY1E3UVJsZi93S05VSFBmNGovUmR1KzZUL1dtSEFhZlNzb2dDODZKTGhkMU5Ib0JDQi8vTzJoS3YzT2pmK3dBeUZKRXcyWmFodDJ1VkxVOVhmME84d1R4MnA2ZW1HdlFSa1pSbzVmVVVaUHhSQ2dKeG15ZjRrZnEyd08rSDVicFJLbEdsakMxcnJ6VWNic1pONHBYVlJEVHk1eko1UXFzK0JJcXR1VkhQdEZLek1aOWN2a1dGN2wwRFVpMWhBQmJXR3NxK3RPbVcydllsblMveEQxbnNrbHhKck11M0s5eDR3a1hISy9ucFhvTStFWFNxVk1vbjVEK2t4OHIzbzVOMjlqaGFXcEswckk0djh6aWZpUUlRbUFVV3BRRUpNdWUxNXZRVDlJbmFSUVhuaDNJNUViWUF1NUpZbm0rYkpaQzh3ckZkZmkvY2lHc0NWeDRKTWx1b1ZvYmkiLCJtYWMiOiJmMmQ1ODZlYmY0ZmYxZDQwMGVkNjk5OWZiZWRlMjA2ODhiNTM4ZjM5N2Q4NTg5M2JkOWRkZDNmY2FlMTk5ZGM1IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InprQlFEOE1pT1FpS3d4aUlleEJkSGc9PSIsInZhbHVlIjoiSXMwOWMrY3dONHRMU3BhWWxJZVRSZFVtZ0xxanlOa2wzaktvSmU5NnlkeTRGNzkwQUpCWnZSM1plMzk1SlVCb3MzczMvR0tWUVQ0VXAvV2lhUDdXeGVNdGFybVJVMENTNUd1Z0ZWYm0zRGZpQVVsWS9uUkZXZkJ2V3F5TjVtYlNQM1ZrL0hwYk5HUUlIRlFIQ0liZEo3UE9nYnloYjZNUXM0N2czMEx2QXFqdzJkZTBEMUZYSXlNSStJU0t3NC9jNGcwQnNkTzBzOXBpN0tKM3RJeFFTUHZnaUtuN3pXYW1WdU9wbDluR0tkdXNyR095ZStXRUxsUmhQM2ZaMitrM2JwYlRTNjhZZExnMzhJa0Z3TmhiSENCdlFHUnBPRGovUmxOS1ZEeE5UOTR6UXp5VG1BK2gySk5oYjFGMlRJMFdWODRQQTZOK0I4ZzdJb09BeHpMTFlBby9DRnRqS3dZakN0VW8zSkw4MVpaTXU2UGhyZjl4M1B4bVplU0J1R0hmUTMwYXFiTExrWWhnZUh5TGd2V3pJSVFsQ1B5bldKOFhWK1ZNZ3p2Y3JqczFLMm9QMzFxbEhqdEQxL09QTnhkUGRFOFl3WDdRWnRkKzM5dS8wTjhFUU1yMS93RC9XYm5yWkowZmxsYkEvenRmaTZQQ09zVDFxcENWMVVHOFhPMEMiLCJtYWMiOiIzMTkyZWZiNTkzMTVkMWIwYTJkZDhiYmNkNDFlYjAzNGI0OGFjZjFiYzk5ODRhOWQzNGRiNDU4OTA0NjRlYjZhIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllQRHM2VnRYMkR5OU9WR1orS3p3dnc9PSIsInZhbHVlIjoiaGx6RklOSHRQQXN6ODN2ekpWcFlJdWVHVGRpeWpYT1RLYnlKMjh5c2M2VW5INlZjQXBMRlA2bU54SUMvaisweU42a29tSndCalA4MFMrdXhSSHAyZVBDcmZTN1pWY3ZXNU1pUUdxRS9wU3cxaGM5YTlRc3NNcnFBVFNPSHRoQW1jZDNWRm5EVmlQZW5IblpPV3BOT01yUW1JMzQwVDR6Nlk5eVBzOTNuY1E3UVJsZi93S05VSFBmNGovUmR1KzZUL1dtSEFhZlNzb2dDODZKTGhkMU5Ib0JDQi8vTzJoS3YzT2pmK3dBeUZKRXcyWmFodDJ1VkxVOVhmME84d1R4MnA2ZW1HdlFSa1pSbzVmVVVaUHhSQ2dKeG15ZjRrZnEyd08rSDVicFJLbEdsakMxcnJ6VWNic1pONHBYVlJEVHk1eko1UXFzK0JJcXR1VkhQdEZLek1aOWN2a1dGN2wwRFVpMWhBQmJXR3NxK3RPbVcydllsblMveEQxbnNrbHhKck11M0s5eDR3a1hISy9ucFhvTStFWFNxVk1vbjVEK2t4OHIzbzVOMjlqaGFXcEswckk0djh6aWZpUUlRbUFVV3BRRUpNdWUxNXZRVDlJbmFSUVhuaDNJNUViWUF1NUpZbm0rYkpaQzh3ckZkZmkvY2lHc0NWeDRKTWx1b1ZvYmkiLCJtYWMiOiJmMmQ1ODZlYmY0ZmYxZDQwMGVkNjk5OWZiZWRlMjA2ODhiNTM4ZjM5N2Q4NTg5M2JkOWRkZDNmY2FlMTk5ZGM1IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InprQlFEOE1pT1FpS3d4aUlleEJkSGc9PSIsInZhbHVlIjoiSXMwOWMrY3dONHRMU3BhWWxJZVRSZFVtZ0xxanlOa2wzaktvSmU5NnlkeTRGNzkwQUpCWnZSM1plMzk1SlVCb3MzczMvR0tWUVQ0VXAvV2lhUDdXeGVNdGFybVJVMENTNUd1Z0ZWYm0zRGZpQVVsWS9uUkZXZkJ2V3F5TjVtYlNQM1ZrL0hwYk5HUUlIRlFIQ0liZEo3UE9nYnloYjZNUXM0N2czMEx2QXFqdzJkZTBEMUZYSXlNSStJU0t3NC9jNGcwQnNkTzBzOXBpN0tKM3RJeFFTUHZnaUtuN3pXYW1WdU9wbDluR0tkdXNyR095ZStXRUxsUmhQM2ZaMitrM2JwYlRTNjhZZExnMzhJa0Z3TmhiSENCdlFHUnBPRGovUmxOS1ZEeE5UOTR6UXp5VG1BK2gySk5oYjFGMlRJMFdWODRQQTZOK0I4ZzdJb09BeHpMTFlBby9DRnRqS3dZakN0VW8zSkw4MVpaTXU2UGhyZjl4M1B4bVplU0J1R0hmUTMwYXFiTExrWWhnZUh5TGd2V3pJSVFsQ1B5bldKOFhWK1ZNZ3p2Y3JqczFLMm9QMzFxbEhqdEQxL09QTnhkUGRFOFl3WDdRWnRkKzM5dS8wTjhFUU1yMS93RC9XYm5yWkowZmxsYkEvenRmaTZQQ09zVDFxcENWMVVHOFhPMEMiLCJtYWMiOiIzMTkyZWZiNTkzMTVkMWIwYTJkZDhiYmNkNDFlYjAzNGI0OGFjZjFiYzk5ODRhOWQzNGRiNDU4OTA0NjRlYjZhIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495931984\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-872651586 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872651586\", {\"maxDepth\":0})</script>\n"}}