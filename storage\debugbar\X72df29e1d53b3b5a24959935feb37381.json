{"__meta": {"id": "X72df29e1d53b3b5a24959935feb37381", "datetime": "2025-07-12 15:41:05", "utime": **********.210912, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752334864.777675, "end": **********.210925, "duration": 0.43325018882751465, "duration_str": "433ms", "measures": [{"label": "Booting", "start": 1752334864.777675, "relative_start": 0, "end": **********.137364, "relative_end": **********.137364, "duration": 0.35968899726867676, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.137374, "relative_start": 0.359699010848999, "end": **********.210926, "relative_end": 9.5367431640625e-07, "duration": 0.07355213165283203, "duration_str": "73.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48170120, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.007280000000000001, "accumulated_duration_str": "7.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.170197, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 24.176}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1796741, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 24.176, "width_percent": 6.456}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1921182, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 30.632, "width_percent": 6.593}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.193776, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 37.225, "width_percent": 5.495}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.198153, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 42.72, "width_percent": 37.088}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.202858, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 79.808, "width_percent": 20.192}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1572626688 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572626688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.197239, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-813026889 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-813026889\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1187792146 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1187792146\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1061469464 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1061469464\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-905369050 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlEZEZZczdwMkVUZFd2ZlhxRXBOOUE9PSIsInZhbHVlIjoiY3dlUzVFRnZEL01pVnA2VGZsUWE4U3A3anE5bXNVbW9LcUI2bEcyMlZGQlJYemxWNHBIYmExT0g2MFdYV2ZYTldhRVFaRm12MWpHbmlISm83ZlNVUHpTdFpMS3hoLzZSRkc5NGs2VTJWQnZLTWJ5OVJUVlhFRjNYcDZhR3o4OWl6d3pLVHNhaDNQa092cStmaVR5czNTTlVYQnp5Y3lQOE9pa3JJNTd1cUo1YXBMNE04ck5wYk5WZmZPNWdkR3REZXRSeGhITTJScStROE9pblN4ZUNWUXV4OGFQcjVrZW1EVDJGTmlOK2R5TENtWk40ZFpRWEJZU2N0R0hDb0JJR1liU1lCQ2xuUmZGZDJITTJGcExXMEFGbkh5bDIvUFBZTnFTTG1hN2RESEwyeHM5MnRhUkdvOW5QNlFNT253STh5MTNLUy9qREczc1V2WjJWdW9lSzFQeGN1d2dvRGNCUE1LVTRRYkQwY1huZk5aSThFZjcxMG5yMXJRUlVrdjJtUzRLMnc1SWo3TUdLdldzNkExUVZvcklPcVdqMXk4V2kwY0RzeThIbGc2ZmdoemlqZjBFSXVQekdtRXUxRGExRyt0dHo3ZHN3NFU0OUZRbVhwYUJobDFCa1hLdWxaNWQyUjhjWFkrSUM0ZkdQNGZGUVVlckNnRDZ3MUJMSVBzNG8iLCJtYWMiOiIzMzJmMjZkZGVkZTEwMjQxMDFlMWY3NmUyOGRiNGVlY2UxNTM1MGNjMThkNGQ2OWIwMDU0NjNlYTNlOTYyYmQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iml4bms4TUMzTzhKbjJpc1g4NHJFdlE9PSIsInZhbHVlIjoiWlpXem1jSHJ0b0lvdEs5VVB0L2Nvb05ia2FqSkp6T3kzV0VZV0taK2kzamp4VHB6OXc3YkZadlNMN2hLYkF2NUxRQUNkZnIvQU5qOVVaWUNTR0xwV3pFVHo4KzJEblZreXNsVDFxaTBOb3ZOeUZ3S0dmTDMwN2NwUElzdHBsVWhpZzBGd3E3OEhKVURIRzlBVVhsdW1kK1RaKzhNNVNWSndmeURST3pwNi9GSHVSaGY5YU12ZXp4Y1FhZUFDOWZwUldEcjlSdlk2YWEvVWVmeVRkU2JYVXVzWThLU0tzVHo1b0ltV2xYeUVVclZJSG9RVXlDUFJBNXd2RlRBbjk0THZnZUk0eGpPZGtkTi9OUkhsazJWL2ZjUnUxelJhVTkwMTNvVE1iVjJaZTQ1MExobW9hcXBXV043Y1EyelpDK29WdEwxQmZ4bWVUc2UzdUhDcFNRbnVHbGEvSFhVOXlFbWE4QkdEYVM1WFE4b3piUDFYdXkzSW9VNWE1S0pnYytjbld4YXlZNmtEWUhrQ1RvL3ByQ2RtUE9Fd0dzS1ZCOHB4eUpuYUNNTE00OXRCeFN5VzdSQ3Fma3cyZ2dDMDV2M0F0QUZIQk14T0l4QkFSV2J5YkhMQXFETzlvYjVHWkZFT0dPVzEvRC9XNkY4clBZbWl6UlM1YWFXSGtsQmdweFgiLCJtYWMiOiI2NWI1MzY0MjgwYTIwNWViZDdiOWUzNGVhMTEwODhlNTliZTU2NjZmMjVkM2Q3OTEwMGMwZTE1YTQ3MjA5ZTkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905369050\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2007570761 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007570761\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1136808917 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:41:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1QSk1rMjRCb1VLWlAvZ01QY2FvNWc9PSIsInZhbHVlIjoiYlVDTmpBckxDVjFDbFRKNHFFSXE3U05nZFc5T1R4b1hualNSbWRnVlNJZC9pbFNDVjVjUmNicEJpUnRtYm40TTRtMHZpd2p5eGtMNy90MldNTUxCMnRVQmpnZG9JcEFnRmdTZzhZaWRRZ0xHWS9VaW5BQkpWekZxaXNHVnkva3o3STJycXFCeTBuYUxrMTR2K3U2M2NOK2xLVnA3R2NHL3ZhOWsyOTJYZ3dWdHBLQ0ZPTjVFVi8xb3FBN3BtUmxLTXZDV3RWUGxnVkZRbmZzRjVnQ202dit3cVErWFE3amp4QVplaFBxZWNLcEhYMnh2c3o2V2t1djNQaytFSnFmWUg4YXgwSDAvV2JCa2NjeTlEYVBGcDVJdVI1c0JuZkxWblBKRzNKQlptd1hkazdzSVlKK3grcXM1eGN4eWVZUEptOFNremlOREwrcmxHcVQ0Q3hpeXdVN2lJeXdTbUczZldWODI5TjByektnZkNaaHNxS09JaHRtd2JKYnd3bzg2NzBYZUkwdGhzck9FWnFGblVsejd1VFJlZVZaeFE2a2hDbU5kanNTM0dHcDBtTVA2NmE3ZDlzYmswZEJ4RjZBVEFWZ0ttVnBXUitya0x4NHdIeFhVa0RYd2FxbThNcnVKTG9CazZTQUtHTVFTM3pKMlBtWmErNVhyc0loRCtOS0oiLCJtYWMiOiI0NmRkODQ2MTk1NzYzOTM1YmUwNzRjMWIxMmQzNGIxMjBjYzRmODI0ZDVkZmVhMTFhNGFlOGFlNTM3YmE0YWE5IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:41:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhHU2RXR3EvcU91cy9HQURiRnh0T3c9PSIsInZhbHVlIjoiYTdmS0lVZjJxR0lXZmlrT2NtejUwM1g1VUpTc1RDa1luMjRSNjJXUEtHNFVvWVNvbWFFLzYveUl3Uk1yVnR4RlRZbllheUhxSHIyVXNpNDJNbEx6di9wemQrYll3c1UzbE5oSFJOdzEyOEEzeUxnUGZhaTQvbC8zR0R5aXJ2VVFoN2xrSTQwL3RnVm9PL0ZJV0hkd0k0dkdJMWFHdWlGTHdDclFGSDlUbktWQ2pjMDZadnk3QkJ6U0lOWkkvcDNkcGk4SHNLczF5K0pIbjgvekdZMjRyTjN0MENPeFFMeFF5Ymd5TWJQRlhpallRZ2FwK0s2dHk0cnUzb3QzWTlaV3RIa2l5Nm4yUmZ1cy82MjlML2haMldXMXl1TWNWUHp2NkE4TnpkMGtKaWI4d0JxRVZTVVZWaXdFOUVHM2NodnU1YWJWcWlCblpzUUVscjh0VWt0Q040WE45RDVyRHNZTDRHVHRvcnlaR0w0TCtwMXhSSmU4MzZuUlJzZXcxYjUyR2IrajExOEYwU1ZubU9qYlZWVmRmZ3F2UVp1OWlLbkhrQlA1TTcweS9kZVluQ25ScE9PNE40RFAwUDNlSnRXdnpDMlVybjl0cE1mNGFpSFU1Ymd1ZjQxY2taVkRtVXgxVy9oVW5vWlhMcG55QlMwdzBsUVJzNGlsZTJrT2ZUbTIiLCJtYWMiOiJmN2FhZWRhODYzZWQ4Nzc1ODRmNjU4YzczMjYxNjY2YjNhZGRjYmYzMjcyNzBhZGM3NmI0YmI5MjNhOWU1MGEzIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:41:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1QSk1rMjRCb1VLWlAvZ01QY2FvNWc9PSIsInZhbHVlIjoiYlVDTmpBckxDVjFDbFRKNHFFSXE3U05nZFc5T1R4b1hualNSbWRnVlNJZC9pbFNDVjVjUmNicEJpUnRtYm40TTRtMHZpd2p5eGtMNy90MldNTUxCMnRVQmpnZG9JcEFnRmdTZzhZaWRRZ0xHWS9VaW5BQkpWekZxaXNHVnkva3o3STJycXFCeTBuYUxrMTR2K3U2M2NOK2xLVnA3R2NHL3ZhOWsyOTJYZ3dWdHBLQ0ZPTjVFVi8xb3FBN3BtUmxLTXZDV3RWUGxnVkZRbmZzRjVnQ202dit3cVErWFE3amp4QVplaFBxZWNLcEhYMnh2c3o2V2t1djNQaytFSnFmWUg4YXgwSDAvV2JCa2NjeTlEYVBGcDVJdVI1c0JuZkxWblBKRzNKQlptd1hkazdzSVlKK3grcXM1eGN4eWVZUEptOFNremlOREwrcmxHcVQ0Q3hpeXdVN2lJeXdTbUczZldWODI5TjByektnZkNaaHNxS09JaHRtd2JKYnd3bzg2NzBYZUkwdGhzck9FWnFGblVsejd1VFJlZVZaeFE2a2hDbU5kanNTM0dHcDBtTVA2NmE3ZDlzYmswZEJ4RjZBVEFWZ0ttVnBXUitya0x4NHdIeFhVa0RYd2FxbThNcnVKTG9CazZTQUtHTVFTM3pKMlBtWmErNVhyc0loRCtOS0oiLCJtYWMiOiI0NmRkODQ2MTk1NzYzOTM1YmUwNzRjMWIxMmQzNGIxMjBjYzRmODI0ZDVkZmVhMTFhNGFlOGFlNTM3YmE0YWE5IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:41:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhHU2RXR3EvcU91cy9HQURiRnh0T3c9PSIsInZhbHVlIjoiYTdmS0lVZjJxR0lXZmlrT2NtejUwM1g1VUpTc1RDa1luMjRSNjJXUEtHNFVvWVNvbWFFLzYveUl3Uk1yVnR4RlRZbllheUhxSHIyVXNpNDJNbEx6di9wemQrYll3c1UzbE5oSFJOdzEyOEEzeUxnUGZhaTQvbC8zR0R5aXJ2VVFoN2xrSTQwL3RnVm9PL0ZJV0hkd0k0dkdJMWFHdWlGTHdDclFGSDlUbktWQ2pjMDZadnk3QkJ6U0lOWkkvcDNkcGk4SHNLczF5K0pIbjgvekdZMjRyTjN0MENPeFFMeFF5Ymd5TWJQRlhpallRZ2FwK0s2dHk0cnUzb3QzWTlaV3RIa2l5Nm4yUmZ1cy82MjlML2haMldXMXl1TWNWUHp2NkE4TnpkMGtKaWI4d0JxRVZTVVZWaXdFOUVHM2NodnU1YWJWcWlCblpzUUVscjh0VWt0Q040WE45RDVyRHNZTDRHVHRvcnlaR0w0TCtwMXhSSmU4MzZuUlJzZXcxYjUyR2IrajExOEYwU1ZubU9qYlZWVmRmZ3F2UVp1OWlLbkhrQlA1TTcweS9kZVluQ25ScE9PNE40RFAwUDNlSnRXdnpDMlVybjl0cE1mNGFpSFU1Ymd1ZjQxY2taVkRtVXgxVy9oVW5vWlhMcG55QlMwdzBsUVJzNGlsZTJrT2ZUbTIiLCJtYWMiOiJmN2FhZWRhODYzZWQ4Nzc1ODRmNjU4YzczMjYxNjY2YjNhZGRjYmYzMjcyNzBhZGM3NmI0YmI5MjNhOWU1MGEzIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:41:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136808917\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1867631084 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867631084\", {\"maxDepth\":0})</script>\n"}}