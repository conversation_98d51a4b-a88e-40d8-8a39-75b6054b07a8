{"__meta": {"id": "X72e013b9a4600a57f20c2179da67abb0", "datetime": "2025-07-12 15:48:01", "utime": **********.923407, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.468437, "end": **********.923424, "duration": 0.4549870491027832, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.468437, "relative_start": 0, "end": **********.830921, "relative_end": **********.830921, "duration": 0.3624839782714844, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.830931, "relative_start": 0.36249399185180664, "end": **********.923426, "relative_end": 1.9073486328125e-06, "duration": 0.09249496459960938, "duration_str": "92.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48170152, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.019389999999999998, "accumulated_duration_str": "19.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.867785, "duration": 0.014119999999999999, "duration_str": "14.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.821}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.889661, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.821, "width_percent": 1.908}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9025671, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 74.729, "width_percent": 2.476}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9043229, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.205, "width_percent": 2.011}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.908905, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 79.216, "width_percent": 13.409}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.914284, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.625, "width_percent": 7.375}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2090879661 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090879661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.907887, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2143722777 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2143722777\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-838468746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-838468746\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1506904585 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1506904585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1709185347 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZYbzd3UnZpWTdQazNpVzRnVy92a2c9PSIsInZhbHVlIjoiSUUvWSt0THVjd0tKNFBGTHhrelJBRHRJbThsSmt6YUFXNWNySUw5UTk3a0luWC9XYUZIbXBKR0FMNHVVZFE0MGZJTHQ2NGZleDdscEVkTHNJVWJHdjdFZmZucXF4ZStNZEdjNUt6QllTd0IwSVRuSGVNQTU4ZHNwT2k3aDZBWlVFQ3p2ZHlJclIzcnprSjZ4Y3AwOUtzNEVCalFjUWhGcnFTQm5DM1F2RWVuaXZrV1RkdTNxcUYrYUI2d3R2VTBaOVlpZWE4MStMais4a011WWRGTU02dGtEcm1Fek9IQjVLMWIwNm1mV0RWREJuUHZpZ0MyWWVoTlk1d2s0U1dvZU1XZFRCbVc2NUtPektxQUM0MG9iVWhiNUlDVTNGR0Y2cFlSZHR4ZWt5N1h5VHlJa29VTERodjc4TTUxS1NTTFhpVDF3Y3Bkc3NmcTlBL2dNbVRjZEtGK3lOM1ZkZmVMOUFBQitmRytlWDQraHhOOVFUQ1lRcWVyWnhHdEhESTY0TVhOck1GRU5oTC8rSHUxUGtWbGlUeTlaYjV4YnBTOU05bmtTWXZXMTkxUy9HNm95cUhHQmY2UGJhV0pRZFVwWWxqdG5wdWJ3RXN4M0MwT2QxR1A2RGxFZ0dNNlZQZUI0WCtRUERmZk1nVVRSdit0QnRJNUpoWmVFSjFuOHJodVoiLCJtYWMiOiIxMzdkOTlhM2Q3ZjY4YmM1MmFkZmJhZTZkMmY1YzFmNzYyMzVmNWZiNWQzOWQ1MjJkZmU4ZDNmOTA4OTE5ZTQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InYzY2xtR0tlOFI4MndQMTNLTTNNd0E9PSIsInZhbHVlIjoiZVNYOGZ4VGwwa0dtQ256MzhRd29mLzFPak5ObWFIUEhlbUVHY1VxYk5mUS9ORTlUTXhSeUN6WWtiM0RlWTV3bE9DMzA2eDRYRm1tNXNRb2p0WXJZZzNtemxBZ1h0cjBZNHIrNDNQOHJFTjJKNWx6b3V4TWFDV0JXL1V4dFdPbXpRdmxtSXVRdzQ1QXFUdTQxRmxadDJ2NjFqeDNsRElYM3A1dkh6SFRpODlwN2hJcTZ5VzhKTTdCNTQxeUFPbmErUkdrZHU2M2s3RDN1Y3JrbWlCRlZUMTIyMHhSb0tNNHMrTWcyUUV5eXptbk81amdKaFpiK01NVjFxcUtCRWdxaVk5QlpCRERuVEl6bXduTllnTVJqeENPZjBVSWlVRVBLb2svMVFSODM2SEgwcjE0cWRhMUo3ZWxyclBXYW4wYXNNZDR3ZnUzWkhFWHE0bGJNTFFoV0JRSERQSEY2UjhQOXNyTkhHdzNqenhoUDhGcGpkNm0zNWVWMXhPbFhJZXdpVGMzRTExanhZZXcxTzRDTmplMmk5a3ZpcTNaMGlnTy9hUmVobkYwTFJnWDRqNTloOTVSNXJEdTM4Zzh6L1liNHdNdFFmRFZtMGtqUUtqVG1leUtrbThyYmwxWjlXMWQxb0FpWUYxdUoxQjJKbmRjUWNLdjVSK2ZNNnNGcjNlMDYiLCJtYWMiOiJhNzRjY2M2NjVkNmU1MTVhNDBjMGM5ZDhkMTY1NTVhNjQxZDhkMzI3ZjQxY2ZiMzljOTU1ZWYwZjJiN2EzZWY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709185347\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-282728957 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-282728957\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-665015778 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:48:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InN0OTBNcUtVY1FXMk9wUUxBWEtOa1E9PSIsInZhbHVlIjoiYWVFNUxPL0hQV0dGd2Irc2RVTGFTZGpDVzZhTGJHSDlKTDl6OGFZN0I0L2o5Zk5NRUI4enQxMDFsbUx3bjJnVTVVYmhIeEVzTVZpOTBSTFZoWkNxMkM0Y0JiTEZJU2VRQnNiSGZHbGtuZ3prc1BsRnluTHI2RGlTZ016RDJVM3lvbmE4TStmanFYVUtFRGN3cW1KOVZsT0wzSTlBZTRMdHNBd0J6OG1jZVBWR2swM1VHK1pWWkxkWUJ1ZkRlTm11TVU3NGNTRkg4TFBkUlpTRDBBcGIrVSs5bGI3TzhmS0tmQXRla2g4cnRuRXRMYjhuQjdYTzhXMWM2WmI5bVhuZHZQYWNabURLSm9MVnoxaWVpV01KK2JpTTJhRXhrL0hOcWN2SXFnakh0SjhCOElkVGQyQ2pFMk93Q0dFRGZmSHl6S3dVemJhV2hlQXY4SUFidWhKZEVOS04yRm1PWTBTY3pSMFZ0RlRFOTEwbkVtUittc25aTkV3UWYxZWdIeXpvUlIyanpDWWF1Vk0wWTZNQlVVTmRzUmhTZ0g5cFdZc245UjcyL2hEcFF4WVFZNDdRbWhJZ3RHQ1BlMmlLeW9FRkI4UUdjZ2lFOWJZUTZzQzdQejhBZWZ6dG5Zbyt1WHllZlE0cXRuTHU5VEY1QkZhSFpSdzlwdTFoNlZ2QjVxRzQiLCJtYWMiOiIwOWM0MjcyMDczMjliZDI3NmU4OWFjZTBjZjI5MjkzOWY2OGQ5MmY0OGI2MzE0ZDg1MGY1Y2M1ZGNjNmNhOWQ1IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:48:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJrQU5pVjlTeWltdUpTNUNGNjkwWXc9PSIsInZhbHVlIjoiSG1NaXdEczBrSmVGeCs2cS9hNkVldUd6VHk3SDBjV0VQRW1zb1grbWJCNDgzWkMvNndGV3h0NTE3Q0VYZ21GWjVjNzVwd20rRG41cnE2Ry9wY2daaTJETG40YTc0alBRUTRPZENIZ0xzQi9BQnR4WW5hcytUdktUcEJnaEFFZm0yaG5wM2daV2k0bFZVdWY0ZTVDYS9PeXd2b2tldVlmQ2l2YWhVMGRodzh5K3VJL2JOWjAvY3pGT3NKUm9rK2JZN29NQndwaWVvU0paRjRSUkJWQWFsN2lkUlBUUm92ZnlSSzJsMVNBMlVjVjdRSndZTk00ZzQyVUVuMG9YWHY0ZjJWRnRlTlQ4V25oYUZ6eFJJdi81YkFjYkd4OHJzVGRLSndTOWVNMmJvM0RqYmd1TnQyc1VmSWJuZ0w5UDB0NDRmZ2g3bkRRZnJXamk2SXZkbGRHZElPaGh0UXUyM2tITm5OdHdhRVQxdDBoR0V4UU52a0JFWjIyNVpnK1JqQU4zUTBMYzQyd2p1N1pQeWM2ajBHeEswWitKcXlubGc0NyswSFluTnFzUG1IK2ZsM3JrUjRXQ0FKOVlNcVRMK0VkVVBYSWRSamlZamxMdXIrb3NTSjFNSFlxZnRscTVCYWlmbXZ0QWJDTnNvei9kdk5ZSTcyYXZtbmxIMi9PNFowOGkiLCJtYWMiOiJhMjk4MDg0MDNmZDhlM2QyMzVmNjViZmY0NzVjOTM3MTNjN2UxYzg2ZDVhZDU4YWQ0OTIyOWM0M2M4OGMxZjUyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:48:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InN0OTBNcUtVY1FXMk9wUUxBWEtOa1E9PSIsInZhbHVlIjoiYWVFNUxPL0hQV0dGd2Irc2RVTGFTZGpDVzZhTGJHSDlKTDl6OGFZN0I0L2o5Zk5NRUI4enQxMDFsbUx3bjJnVTVVYmhIeEVzTVZpOTBSTFZoWkNxMkM0Y0JiTEZJU2VRQnNiSGZHbGtuZ3prc1BsRnluTHI2RGlTZ016RDJVM3lvbmE4TStmanFYVUtFRGN3cW1KOVZsT0wzSTlBZTRMdHNBd0J6OG1jZVBWR2swM1VHK1pWWkxkWUJ1ZkRlTm11TVU3NGNTRkg4TFBkUlpTRDBBcGIrVSs5bGI3TzhmS0tmQXRla2g4cnRuRXRMYjhuQjdYTzhXMWM2WmI5bVhuZHZQYWNabURLSm9MVnoxaWVpV01KK2JpTTJhRXhrL0hOcWN2SXFnakh0SjhCOElkVGQyQ2pFMk93Q0dFRGZmSHl6S3dVemJhV2hlQXY4SUFidWhKZEVOS04yRm1PWTBTY3pSMFZ0RlRFOTEwbkVtUittc25aTkV3UWYxZWdIeXpvUlIyanpDWWF1Vk0wWTZNQlVVTmRzUmhTZ0g5cFdZc245UjcyL2hEcFF4WVFZNDdRbWhJZ3RHQ1BlMmlLeW9FRkI4UUdjZ2lFOWJZUTZzQzdQejhBZWZ6dG5Zbyt1WHllZlE0cXRuTHU5VEY1QkZhSFpSdzlwdTFoNlZ2QjVxRzQiLCJtYWMiOiIwOWM0MjcyMDczMjliZDI3NmU4OWFjZTBjZjI5MjkzOWY2OGQ5MmY0OGI2MzE0ZDg1MGY1Y2M1ZGNjNmNhOWQ1IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:48:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJrQU5pVjlTeWltdUpTNUNGNjkwWXc9PSIsInZhbHVlIjoiSG1NaXdEczBrSmVGeCs2cS9hNkVldUd6VHk3SDBjV0VQRW1zb1grbWJCNDgzWkMvNndGV3h0NTE3Q0VYZ21GWjVjNzVwd20rRG41cnE2Ry9wY2daaTJETG40YTc0alBRUTRPZENIZ0xzQi9BQnR4WW5hcytUdktUcEJnaEFFZm0yaG5wM2daV2k0bFZVdWY0ZTVDYS9PeXd2b2tldVlmQ2l2YWhVMGRodzh5K3VJL2JOWjAvY3pGT3NKUm9rK2JZN29NQndwaWVvU0paRjRSUkJWQWFsN2lkUlBUUm92ZnlSSzJsMVNBMlVjVjdRSndZTk00ZzQyVUVuMG9YWHY0ZjJWRnRlTlQ4V25oYUZ6eFJJdi81YkFjYkd4OHJzVGRLSndTOWVNMmJvM0RqYmd1TnQyc1VmSWJuZ0w5UDB0NDRmZ2g3bkRRZnJXamk2SXZkbGRHZElPaGh0UXUyM2tITm5OdHdhRVQxdDBoR0V4UU52a0JFWjIyNVpnK1JqQU4zUTBMYzQyd2p1N1pQeWM2ajBHeEswWitKcXlubGc0NyswSFluTnFzUG1IK2ZsM3JrUjRXQ0FKOVlNcVRMK0VkVVBYSWRSamlZamxMdXIrb3NTSjFNSFlxZnRscTVCYWlmbXZ0QWJDTnNvei9kdk5ZSTcyYXZtbmxIMi9PNFowOGkiLCJtYWMiOiJhMjk4MDg0MDNmZDhlM2QyMzVmNjViZmY0NzVjOTM3MTNjN2UxYzg2ZDVhZDU4YWQ0OTIyOWM0M2M4OGMxZjUyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:48:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665015778\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-890330628 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890330628\", {\"maxDepth\":0})</script>\n"}}