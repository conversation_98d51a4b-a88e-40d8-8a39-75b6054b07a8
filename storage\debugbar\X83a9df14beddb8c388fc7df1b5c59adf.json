{"__meta": {"id": "X83a9df14beddb8c388fc7df1b5c59adf", "datetime": "2025-07-12 16:06:09", "utime": **********.0484, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752336368.601358, "end": **********.048413, "duration": 0.4470551013946533, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1752336368.601358, "relative_start": 0, "end": 1752336368.991531, "relative_end": 1752336368.991531, "duration": 0.39017295837402344, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752336368.991541, "relative_start": 0.3901829719543457, "end": **********.048415, "relative_end": 1.9073486328125e-06, "duration": 0.05687403678894043, "duration_str": "56.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45365408, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029100000000000003, "accumulated_duration_str": "2.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.027094, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.698}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.038888, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.698, "width_percent": 18.213}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.041807, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 85.911, "width_percent": 14.089}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "pos": "array:1 [\n  2421 => array:9 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"2421\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktEa2sxaS94YXFKYTZoVmRFaHk0eFE9PSIsInZhbHVlIjoiOU5GekppeEJhbndFY0gyUElRQWpyczMvNkU3cUhKVlV1MzRMbWgxSFA4Zm5MME90dVlsNEtVaHZGTm13Vzh4bktPTEIwSzFGbWlYOVNmVXFRZysxdmdqOWFNcXltOXQ2SURSYXhFM3BPMXhQVmM5ajdEdXVPYWEwOWh2ZXZGckRuSXlkOGhtc0NLalJPa3Z4MnZrVUFIVUxwNDlKN0RmbndYNXlTaEh5b0JKdVFQRy9LK2R4OTM2Z0VQb2NXWFVJWmExRlNsWGpBOUI3SktOSXkwNUtBS1NZcjkybUdackNlS3ZiWVN5MUYxWWprbExyUXRacFhBT25veEpxbDNTdjRxR1BiZGdSNTM4cUxxZ3NjYlJ0ZWRONzZxKzAveVpaUlU3Y25Ia2t1VHJ5dm1LRCtIUmxoYlB3azdybVZNZ25KRDJwTWlYQVBDV3FFckRJOXFodGlqOVhMQS82THhKaml5bmg0L09WSEVmeVBkU2Q2K1pjZW9Wblk4bWF4VDlFaDZMVXhKUythM1JEUFZRZDd1V01uVjZDSy9KeUNUbVhNTUZTVzJoc1orUVBYUDdVUW5oMjU2RTU1c3FiWTdZYXFGcnBwbmk2Q3ZhR1ZnUk50SkluNS9ISzB4QSttcW55bG94c0hYTisrNUhqci9lSy9RMjlYMEtZZmkvZ0Qwa3IiLCJtYWMiOiIxMGFiY2QyMDlmYzkzNzljMWJlM2RlNGFjYmY4NGUwNmE1YWIxNTg4MjYwZDJjY2QwOTJlMTE0OWRkMDY4ZTdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkF3MWFCM29GRkhlYjF2dllIejczS3c9PSIsInZhbHVlIjoiMEpKYVBoR0pBZnUraTlIWFhwZmgyVXhmSzZQa2pwdWVHbmtVNldvSWlTZmxHTERnYlN1UXM4NVJMRWw5NmZDSGZVQm1pRkRhOHNBaFlIb055V2lBNnM0S3RRTSsvNkV6dFBJWXRtTVNzNDI0enVqTUNXakltOHlrVG5FbWZvM2QxenBCcWE5L0RiUEkra3VyZ1hHUW9sdTZDb1U4cGFzTC9BTzE2YkJuaUdaTlpXcGpxVDhTc3ovZWpHNFdKY0RKWjE5Y3g1NDFxN0ViU0pueDNyUU4wcWZXV3psOU02Z1Bjcis3OGk4S2V6cTd4dzRrelpUallCaGpMakxaRVcxMmdyTGhKOTdCQnhHSm5qeDM5ZUVlSGZ6cUpIbHJIYjFFUmRjRzF5WDM5Nm1QRmhFaytzYzVMRm9CUUtrbzh3dTUwbEwwcGFqWGVxbmhCWEhxZHNiY2NmUS91V0ZJUVVVZUZPREkwY2tZd0JnZ1BGYWdNbkZZbERJdEVQUUhZb2JIaitYOGtwMkdSeXNsSk1pN2Rxa3Z2MjAvL3hKV0ozV3V6OVpzWHA0VGtQL2Q5T2VQMDltRzdveW0wK0hMNjI1aFo4UlJWalZGZEN1eDVPdHc2QVVqcXlOV2Q0ZVdTdXE2Yy81eWZUNUxBL1dESU1JQUIvZlNFVFZaanlucUFkaHciLCJtYWMiOiI1MGM4N2FhNTJjYjZjOTQyNTIwNzViYjkyYjIzMDc0OTc5NjRhZTNmMzZiOTQwOTdhNGYyZDZiOTc1MjUyYjdkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1042735912 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1042735912\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1793696750 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:06:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklEalk3SjljNDEwekgvMnZoNnl4b3c9PSIsInZhbHVlIjoiN1BTZnRMcHpkczRkK1ZaRkRkNlRBcEJ1cTcwVzlaOXVWRHN5V3lzcjVNRytPS3orT1BlaTArd1RGK3lQc0ZCQ0ZQMEtzWDA0WGIyR1JVMUNlbjNhaFZjZmdNbzNQK1A2WkliblhzMERmQ3hDYitMWVVGR3k4SW9iUWJOVFd4cDJ1dFFaalkrMXQrNDdteTc0UngxWFZwWXY1STNvQVE2bFkvMDFsM1hMd051ZlAvc0FSNDNZRS9tSnAzemFsZXV4TXF6emJodWJTeWYwS3hPQ1ZwUzcyM0FFaURSa1M2WVZzenVjRzlRaGdBZTVXVVJCd0VQM3JmMUoyL2Q3elV5NUVoSmR6ZGxsL1JHdnExMnBWZlFiM0JkTXFzMUJpaG1DaWtBc1d4YkxzQThjWkN3dVpBZmYyc0xYWUJIZHZiQXBOcmRqK3RlV21NaFNrQlBOVEhJNUs3bmQrZlpHdUNmeG9YbWhPWnA5R1dvcmZMclY5ME5vdS9iQjNaUndzN2pDWFFQdExybmdWQTNwMGo2VHFVZ2JQS094MjQyUlhPQnNkdXlkV3ZJbStmQmNMZ3dZaDFXaTdHdHZpendXMzJwcTFFWlVQWXpsTXBncGpwanR4TEpIbXp3Z09IRXpYQm8rdXA3MGpia3BNVGZJbTN4eVM0MFJuY3NhMlRNeVJvc1oiLCJtYWMiOiIwNjlmNzcxMzM0NDE0MzIzNDkwZDkyZjdiNTI5YTRiMDQ5MDIxNTg5ZDZlYmRiZjI4MjMyMzFkNzI5OWIzMmMyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJjOHJGWlFPSXhxeUZPZzRnT21WUnc9PSIsInZhbHVlIjoiS2xveGpvYzlXcHJLSHQvUWp3azM2aHE4TnVNQjVKWVBjd0pndXpUdS9ud2hXSzZERUNRRmtyaVgxODR4RU9lSFBwUWdtTWtNRXFFSmk1YlI4R3pDY0UwcytBSTB5WjlLcnRnUS9MR0JwamtHMEVzVlA2VHFCa3B0SGppczlyU090d2NJczUxaFRiVkdvU3c3elNMVklDZStrRlhqeVVRQVBmQVBEM0VSYTlCK3lwT1V4OEhIb1E0ZUdoRk5FVmVMeDMyNEJQeXptNGRDY0laYVNqc1NSWFdSNmFoaE0yVmpCcEpEdzJzVml3SUFxQ3g3K004bUR1SFc0cWR1OXpoNVZpQU5yRnBsbDY1T05IeDRuRWFLMiszR0hrcllKRTJmdFVCUjNWOHh2WXZuNE5zeWpxUHlvYmZlY0tqeUN0UXZsS2RVY281WUNZYTU0Y21idlhoWXBHbjJOL0FJZjFCV2k0UjgycEFZSGxZeFNJYS9oK1NvaUNyOUtlcVMxQXFUTjh4NnBJTmlVSkc1N0FUM3daa0R2ZXVpc1lkSHNXZ1BhSkhUNFEvTks0VEgvYVFyOFpZRE45cTFMY3ErVUhFMWl0SWtvTndMM1VwdHJkVzMzZnhzNGFuZFBmYy96RUlVTEt6SzdiUGNzOGkvUVBTcFd5UnlnZ1MrUXNLWWd6SnAiLCJtYWMiOiJhMzZlMjRlNjZmZGY1MTJkNTdjOGRmYjNiMzE5MGVkYTgyYThmNjA0OTQ0NmE5MmU4ZjVmNzhkMWE5MDM0NjNkIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklEalk3SjljNDEwekgvMnZoNnl4b3c9PSIsInZhbHVlIjoiN1BTZnRMcHpkczRkK1ZaRkRkNlRBcEJ1cTcwVzlaOXVWRHN5V3lzcjVNRytPS3orT1BlaTArd1RGK3lQc0ZCQ0ZQMEtzWDA0WGIyR1JVMUNlbjNhaFZjZmdNbzNQK1A2WkliblhzMERmQ3hDYitMWVVGR3k4SW9iUWJOVFd4cDJ1dFFaalkrMXQrNDdteTc0UngxWFZwWXY1STNvQVE2bFkvMDFsM1hMd051ZlAvc0FSNDNZRS9tSnAzemFsZXV4TXF6emJodWJTeWYwS3hPQ1ZwUzcyM0FFaURSa1M2WVZzenVjRzlRaGdBZTVXVVJCd0VQM3JmMUoyL2Q3elV5NUVoSmR6ZGxsL1JHdnExMnBWZlFiM0JkTXFzMUJpaG1DaWtBc1d4YkxzQThjWkN3dVpBZmYyc0xYWUJIZHZiQXBOcmRqK3RlV21NaFNrQlBOVEhJNUs3bmQrZlpHdUNmeG9YbWhPWnA5R1dvcmZMclY5ME5vdS9iQjNaUndzN2pDWFFQdExybmdWQTNwMGo2VHFVZ2JQS094MjQyUlhPQnNkdXlkV3ZJbStmQmNMZ3dZaDFXaTdHdHZpendXMzJwcTFFWlVQWXpsTXBncGpwanR4TEpIbXp3Z09IRXpYQm8rdXA3MGpia3BNVGZJbTN4eVM0MFJuY3NhMlRNeVJvc1oiLCJtYWMiOiIwNjlmNzcxMzM0NDE0MzIzNDkwZDkyZjdiNTI5YTRiMDQ5MDIxNTg5ZDZlYmRiZjI4MjMyMzFkNzI5OWIzMmMyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJjOHJGWlFPSXhxeUZPZzRnT21WUnc9PSIsInZhbHVlIjoiS2xveGpvYzlXcHJLSHQvUWp3azM2aHE4TnVNQjVKWVBjd0pndXpUdS9ud2hXSzZERUNRRmtyaVgxODR4RU9lSFBwUWdtTWtNRXFFSmk1YlI4R3pDY0UwcytBSTB5WjlLcnRnUS9MR0JwamtHMEVzVlA2VHFCa3B0SGppczlyU090d2NJczUxaFRiVkdvU3c3elNMVklDZStrRlhqeVVRQVBmQVBEM0VSYTlCK3lwT1V4OEhIb1E0ZUdoRk5FVmVMeDMyNEJQeXptNGRDY0laYVNqc1NSWFdSNmFoaE0yVmpCcEpEdzJzVml3SUFxQ3g3K004bUR1SFc0cWR1OXpoNVZpQU5yRnBsbDY1T05IeDRuRWFLMiszR0hrcllKRTJmdFVCUjNWOHh2WXZuNE5zeWpxUHlvYmZlY0tqeUN0UXZsS2RVY281WUNZYTU0Y21idlhoWXBHbjJOL0FJZjFCV2k0UjgycEFZSGxZeFNJYS9oK1NvaUNyOUtlcVMxQXFUTjh4NnBJTmlVSkc1N0FUM3daa0R2ZXVpc1lkSHNXZ1BhSkhUNFEvTks0VEgvYVFyOFpZRE45cTFMY3ErVUhFMWl0SWtvTndMM1VwdHJkVzMzZnhzNGFuZFBmYy96RUlVTEt6SzdiUGNzOGkvUVBTcFd5UnlnZ1MrUXNLWWd6SnAiLCJtYWMiOiJhMzZlMjRlNjZmZGY1MTJkNTdjOGRmYjNiMzE5MGVkYTgyYThmNjA0OTQ0NmE5MmU4ZjVmNzhkMWE5MDM0NjNkIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793696750\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-28616087 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28616087\", {\"maxDepth\":0})</script>\n"}}