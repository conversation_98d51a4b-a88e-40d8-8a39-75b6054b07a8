{"__meta": {"id": "X852050376f17159cbca5ae8c0de88bf5", "datetime": "2025-07-12 15:47:25", "utime": **********.122573, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752335244.720685, "end": **********.122588, "duration": 0.4019029140472412, "duration_str": "402ms", "measures": [{"label": "Booting", "start": 1752335244.720685, "relative_start": 0, "end": **********.060515, "relative_end": **********.060515, "duration": 0.3398299217224121, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060525, "relative_start": 0.3398399353027344, "end": **********.12259, "relative_end": 2.1457672119140625e-06, "duration": 0.06206512451171875, "duration_str": "62.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01547, "accumulated_duration_str": "15.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.08897, "duration": 0.01477, "duration_str": "14.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.475}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.112658, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.475, "width_percent": 2.198}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.115499, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 97.673, "width_percent": 2.327}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1304664599 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1304664599\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-652480509 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-652480509\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1132030558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1132030558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-305756725 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFlT3c4QjNSSHZZOEh4eUg1VEFQcFE9PSIsInZhbHVlIjoiQVUxVVRhUVJna1lybjcrOEFnaDdIYytpQk1keU9OSnN2T284aFpkWEFOaUU2WFVQelFVcklEaEI2T2M0cDFUdEdOQTBnS09acVA4M3hmd3EyTzN5NUZNRWIvcEFXemtsNXJoQTBKQ3c2cXhiMFpKaXh0WUZ3MHZDNSsxaEZWaVczdTlBdENyYmJoQngxemUvNFdDRkJsQytCS2pSY1JVRll5Q0txZkRXM0ZvMkEya0ZYYnJMWkJ4bXN4NFAzeC9LbWV5aDNKMUhTYmxqTk9FNmtJMHhxRnhKRFNkM1lSTkJUUHAxeVJtdTBidmo5bmtVVVkrcWtheFZPK0QvaGlGQ0h6NGF1MUtVVFVBSmY4OW5iVkpGbC9ZRXpIaEd6MG9seXdqYzZFNVZqYmVnQUlRR0VaQWFFWnFvcW5IbS9zNFExNGRNYkpJdXV2MjRncTVjVTREQXlsaEhMOVdTREZuaHpPV2dVZThCdHlZUFEyQytidU9sVDNWc2s0UUlJWFZkeFhQcWlZOWhmN3dHelBOWmVFRm1DTUZHN3dOOEhrMTYvVkRrYUl3Tnd2bUJvL0RjL2l6dmVkNmJiMDBLZTU0MVRUdlJkcit6UHJHQ3NDdy9DVlBGZXJJRTIwbXI5LzVZR2QvMm12TnBvV3diaVNKOXMzRm5HNktwSjR2dWltT3oiLCJtYWMiOiI2YjZmYjIyM2YyZjI3YmNjZDJkMjViMDBiM2Y4YTVlODgwMjJlNzc2NWFlMTgwMDdjZGE4ZDQ1YTQ2YjlkOTAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFzY1dPWTZvTHRZdjBoYjJUTVNpZEE9PSIsInZhbHVlIjoibUk5OEFzdTFiTHJ2VmFTZExmWFY1UHdaaEtjZ2hXY3o2aTJHQkJyMTJoUG9SYnNtdmk4bW9SMXNwVlo0NTYrNkozZXFnaDhTWG1mVWt3VkIxZ254MUp2RExuQ0p1bGhCdjJkc0NIaUI3c2FuY3dZeldUbHpTQjRzVGdHUEMxQ3FmeWRtQWs4UFRkR2RZanJ6MkowVUg4d0ljWkk3VUptTU1IWG44UzY5TytUWXR2WWJIL080VjN4eFk1NWxKUDF2SlZaa1V5cU1YOFE0a0J5VTdVZ25ObDB3azczMExxRkQ0Z2hGakhnQmlxUUJTNGtpUE1ZcGdnN0tuSDdOVFNna01VZUtkcEFmdW5JSnBxakpZeTVTVnRYVDNMdlRaT3dMbFRTNnFvVWszTkU3RVZxcnJlMjZVd1hrL1M1WHF3THZXVm9jditGelFISitCblkzakFFR1p4WkJzcE1rR2xINFZCdG5VWWQ1MWlqTDk5NFkyMmg5SW1tdDhYMFVITzNMbWx1ZlFucm9RclNOMTlQQkltOWZHZnpkc245K3lacktlQVBORmpEZ3ZBVk0wQk9OZmNUcTZqY2lMNUI2d0o0Q3lLNGtBRDc5RXRNK2RRT0lhaDIzaWZNQ29Lem1mMVdqcFBpZ1ZzT2RNZjh2cjR1cWFtaG9pQTlGcEhKdG95THoiLCJtYWMiOiJlYTVmZjZiYzFkYTNhZjFkNjJhZjRkOTZkOTg0NDY5ZDE3Yjg5Yjk5YmE5ODE0ZmI1YTYxYWFiMTkyOGNiMmQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305756725\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1763780225 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763780225\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1408585157 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:47:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZqUDk5dzQ1TllXSGdBemgyeTdGa0E9PSIsInZhbHVlIjoiN2dCd3hCaTl6YzRQSDlvNDduMm9PSVVoU0hOVUJMcmFPKzBOVVFqTnBBZFhDWE9iRFZxSHdSV0hRcTNQaG9NeFhZTGpXbnM0RVFtbTZMUU9Hb3RncGpGYWJTRld6ZFF4REIvYUt4bThBVHg2cUtHaWdYVTJQTU9qdmtHdEFDNEw1WVZ3dUV3TzV2eVdydDJGbElwSGlkKzd4c3F6N1VUMEFSS0c4UXQxUHJtYm5zYnNKZUsxOThxMDdnd2lnZzRuUzZtU1g4aXRCYVUvUElrcmJkemNublFUR1JnYWhnWkVTSCtSdG5QMkVMei9WOUV0Vnh6VHVBbGlvTXdXVytCd0U3enN6bEtrc1ZLU1FMNkdHellKMU1RRUxIek56MGlLMzhwcW4yNmlmNlFWeHNwaXBpWHZjdEVwdHFjYS9paldiTnV5YVVidjMrVWtQZWVEYXl2R29Qak1tanF2bmcyekZCY1N5T29YVDdDbm5qUDc1MkRGREF3TnBmZUVaYTBHR3lBcFNWMTVlVkpEQVpnZkk5VVhaeTBoUUFad0xNZFJNRmlrT0hOc3V3bytkYkRteTJlUzRSRVFEWVJCOXhTNy8rYWM5K2dhQmlmOHdCNW4wSHg2bVl5aDdudmplVjhQeU96UEh4KzFmOHorVW8rWEdrYW9hcU04R3hsTXV5UXAiLCJtYWMiOiJiMzNlNzM0YWE2ZTM0MDI0NDVkODMxNjE3YjI2YThlNzg1YWYyNGFmZTNiOGZlZmFjNGVmZWFiYWQ0ZjU0MjcyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpkR0M3Qk53bmR4S0syUHVXVVFHTmc9PSIsInZhbHVlIjoiY1ZWSDB1Tmd3b0ExdngwSU5MbXRYL0RMa2E4UzdGUHlpYUtpVjBlVDJtanJrNmM4V0h3a2djcmNvZkhGRHFaSm9DOXkzVHhjUG5VV0g0VWovS09OSFprYlJnOWZJdGJ0YVJrc0Vxd2NUWXd2WXFEM0lncDdYRDZGWUNOb29HNDBVcmhhN0FjZFpUWTF4WHFIVzVtcGFMMXlYVzF5SlZVY3RIbVBoRlhNeFRER3lFbkVySkRRVUFPbUcxZTVEQ0tLRGhNUFQxUmU0NjdqM3ZDZkxmcmZOYWd0WnRhMDN4Ti9BSDZ0dFFLeDZ1aFZlTUJHODJQbVRGVDBJeW1yY0hlRnpsOE9CZHBzRUZwQ05uY0J2TUxCenBFS2w4UlRJTDBkaXJkWitrTXA3aWYwbEVuTGxzK2FGNHRuOXA0L3hsVmNpWWNSWStjTmZHZjk0WjY4bHNJNjBUYWNpQnRtK2RSSFNkbjdJMzVKSGkxYXE2M1hjMEw3VHRUYUh6cVdtc01JQXVwU3VPY2Jwclh4b3U2QzkxTEtjL1JYQ1QrM3d6MTlGcXR0MjBUSGd2bFJJSG85VmpRaXMwNDBRMVB3RjlUb2ZUZEZPTWNheStzdDM4L1pGS1Uwd2VyU0drc1BuYjVzUkUzUlEzdzVxWlRPakhoamh0bS9ONDFPRHRDM3ZrNHIiLCJtYWMiOiI1N2Q5YzNhNTc1MWU2ZjBlZTI0MmZmZWUwYWE1OGFlNGUwM2ZiYmQyZmRlMzhmYzAyMGEwNjAwNTBmMDBjZjMyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:47:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZqUDk5dzQ1TllXSGdBemgyeTdGa0E9PSIsInZhbHVlIjoiN2dCd3hCaTl6YzRQSDlvNDduMm9PSVVoU0hOVUJMcmFPKzBOVVFqTnBBZFhDWE9iRFZxSHdSV0hRcTNQaG9NeFhZTGpXbnM0RVFtbTZMUU9Hb3RncGpGYWJTRld6ZFF4REIvYUt4bThBVHg2cUtHaWdYVTJQTU9qdmtHdEFDNEw1WVZ3dUV3TzV2eVdydDJGbElwSGlkKzd4c3F6N1VUMEFSS0c4UXQxUHJtYm5zYnNKZUsxOThxMDdnd2lnZzRuUzZtU1g4aXRCYVUvUElrcmJkemNublFUR1JnYWhnWkVTSCtSdG5QMkVMei9WOUV0Vnh6VHVBbGlvTXdXVytCd0U3enN6bEtrc1ZLU1FMNkdHellKMU1RRUxIek56MGlLMzhwcW4yNmlmNlFWeHNwaXBpWHZjdEVwdHFjYS9paldiTnV5YVVidjMrVWtQZWVEYXl2R29Qak1tanF2bmcyekZCY1N5T29YVDdDbm5qUDc1MkRGREF3TnBmZUVaYTBHR3lBcFNWMTVlVkpEQVpnZkk5VVhaeTBoUUFad0xNZFJNRmlrT0hOc3V3bytkYkRteTJlUzRSRVFEWVJCOXhTNy8rYWM5K2dhQmlmOHdCNW4wSHg2bVl5aDdudmplVjhQeU96UEh4KzFmOHorVW8rWEdrYW9hcU04R3hsTXV5UXAiLCJtYWMiOiJiMzNlNzM0YWE2ZTM0MDI0NDVkODMxNjE3YjI2YThlNzg1YWYyNGFmZTNiOGZlZmFjNGVmZWFiYWQ0ZjU0MjcyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpkR0M3Qk53bmR4S0syUHVXVVFHTmc9PSIsInZhbHVlIjoiY1ZWSDB1Tmd3b0ExdngwSU5MbXRYL0RMa2E4UzdGUHlpYUtpVjBlVDJtanJrNmM4V0h3a2djcmNvZkhGRHFaSm9DOXkzVHhjUG5VV0g0VWovS09OSFprYlJnOWZJdGJ0YVJrc0Vxd2NUWXd2WXFEM0lncDdYRDZGWUNOb29HNDBVcmhhN0FjZFpUWTF4WHFIVzVtcGFMMXlYVzF5SlZVY3RIbVBoRlhNeFRER3lFbkVySkRRVUFPbUcxZTVEQ0tLRGhNUFQxUmU0NjdqM3ZDZkxmcmZOYWd0WnRhMDN4Ti9BSDZ0dFFLeDZ1aFZlTUJHODJQbVRGVDBJeW1yY0hlRnpsOE9CZHBzRUZwQ05uY0J2TUxCenBFS2w4UlRJTDBkaXJkWitrTXA3aWYwbEVuTGxzK2FGNHRuOXA0L3hsVmNpWWNSWStjTmZHZjk0WjY4bHNJNjBUYWNpQnRtK2RSSFNkbjdJMzVKSGkxYXE2M1hjMEw3VHRUYUh6cVdtc01JQXVwU3VPY2Jwclh4b3U2QzkxTEtjL1JYQ1QrM3d6MTlGcXR0MjBUSGd2bFJJSG85VmpRaXMwNDBRMVB3RjlUb2ZUZEZPTWNheStzdDM4L1pGS1Uwd2VyU0drc1BuYjVzUkUzUlEzdzVxWlRPakhoamh0bS9ONDFPRHRDM3ZrNHIiLCJtYWMiOiI1N2Q5YzNhNTc1MWU2ZjBlZTI0MmZmZWUwYWE1OGFlNGUwM2ZiYmQyZmRlMzhmYzAyMGEwNjAwNTBmMDBjZjMyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:47:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408585157\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-220027730 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220027730\", {\"maxDepth\":0})</script>\n"}}