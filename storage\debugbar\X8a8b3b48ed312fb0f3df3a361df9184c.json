{"__meta": {"id": "X8a8b3b48ed312fb0f3df3a361df9184c", "datetime": "2025-07-12 16:06:15", "utime": **********.517138, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.060837, "end": **********.517158, "duration": 0.45632100105285645, "duration_str": "456ms", "measures": [{"label": "Booting", "start": **********.060837, "relative_start": 0, "end": **********.438987, "relative_end": **********.438987, "duration": 0.37814998626708984, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.438998, "relative_start": 0.3781609535217285, "end": **********.51716, "relative_end": 1.9073486328125e-06, "duration": 0.07816195487976074, "duration_str": "78.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709160, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020000000000000004, "accumulated_duration_str": "20ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.466331, "duration": 0.01878, "duration_str": "18.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.9}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.493875, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.9, "width_percent": 2.45}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.500385, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.35, "width_percent": 3.65}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "pos": "array:1 [\n  2421 => array:9 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"2421\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-917337569 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1eejy7q%7C1752336369505%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlMTStjNnBJYTE5S21SeW1XemhWTUE9PSIsInZhbHVlIjoiYWlnZmlZRE9CSDROYk5QT0pUSHJXaldhWUlzR2g5Sk5tSGJhTjY5RXoxOVdITDI4NCtvLzBjdWk0YTk2Y0lwVWxEK2NqQmtUZ0xHN0psczdEZk5FT2UvUnBXejdiYUVqUHJXWjlaZ3B2UFZsa0poRDRFN25oYnhYTnlUZDlQVTdHRVdhS2hDSmlCOHJNMEJkOU9tb3BEUEVUa3lLeFVyMFYrbDRQTVQxaHBTdTBFZHAxZEpHRElHQmtMSHFpNm9kZkQ4UTZxR05rY3FBeGVPTzMyV0gxZnZKd2ZBZCtzc1pjb0duOFZ5RmowUmkrV0IxUnBKaEhZVzJsSTcxTVJtUTVQQ1BUOHVWVE5YMGxWTE8zbGxHdWwxT1VwY1ZWSGlqL3FRZUdqNDEvVkpaNEFCUzVIWWtpSXcrSDJ3NktFcC8zM3dOMTE5TTl5WkJJQWYvQlhYZEc2c2wraktrSURObmRFMjRDVCtzK0RLMnFvTlRrT2l4Mk9GSGwvK0c5R1NObmR6SE1PYk9mMzh4ckdjeWpRcWp5WlFuUVlMSldTTGNocUh2S01Bcy9sRUhyOTRaZDNxa0Y5UmZDNzFBaDZxdC9TSVE5SGVTZ0U5NGt5N0ttR2xkMmZHVXBWS2dPbzB2Mk55V3luclhGaDRydHhnR3RLY3MySXZUeEJydElJR28iLCJtYWMiOiJiMmExOTc3ZWM4ODA1ZDlhODkyYjhmZmMxMTViMGI0ZGQ2NmIwOTI3ZjFkOWJjZGU5MmI2NjU1Yjk4YmUzY2E0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjYyeFV5VXh5TERzbU5vSDdFampZa1E9PSIsInZhbHVlIjoiSUt1TVNKWGc4THh2ZjJGYTh2VnhuTUllS3daaVh1MmhHcStQVkphQnN2eCthMUF2ejc5clRHcWYrZXMyNHBMaWp3MngrK1RKNzM2b01lbkRRcWtaQmRqOGhHUkxPQ3FrKzZkUlRsTEhEa0tycmp2aExHbG9aS2R6aTZ4dzNCL2Y2bzVpYzUzN1JMc0F6N1B6YWEzWExPN1YvSTc2RHNzbmNwOWpNbThZdmRkZVhaUysxNTh6L3JJSm9uZk4vZnh4N2IyZ0xDWCtsaXk1N2Y4dWJxK1B1Zml5aDRpWVlxRHR2TUlRbnl5T2l4MHlwNjBpaFcyYWZjTzdXenZtYjdmSTdITzk2TFJLNSs1ZFk1bWoyVGpKSGk1Z1VZdHFVaHMzS2VKY2lSK2xLWWxTR29mWGFXKzdaTHlSUVNTTmtXUTYxcWRrdFdGNHZsVmxuR2d1Z2dKWjFjUlhmM2w2eFlBamE4cEdkWngwcFpuYlR6QTgrTUZzdjA3NlI2d1hlc1NoLzBJNFkySmIxSUtlUFFRbGhxK2lQR0thTHVLbmJEVVluS2RmY1ZmZS83QS9SajNqem5YUU8vSXRKa2RaNFZ0QlJaZGhZVUdrYU8rcDlzU1hRTy9MSnlocUk5MHVsNlB5bW5xKzF1VVJJQndhT0tlQU1TcUE3bU1ZWCt4a1RnRm0iLCJtYWMiOiIyNzY3ZGIzYTBjOTcxMWJhYzZiZGYzZjlhNDM2NGQzNzM2MjcwNDliNDJhYTY0N2I5MjE3MzE0OGQ5YzNhZTJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917337569\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1829658075 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829658075\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1275968614 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:06:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpTZEUzeTQ0OHNmYit2UlNSa3NWWlE9PSIsInZhbHVlIjoiMFh4VGExME84YWlJbnU3d0FiR0Q5cXBPWmFhOE9TK2lLRkRhN2dOTHRkQm5kK0ROeTFTSmpUdWt3WXdQMVNWalJlcS9ldWFISC9SYWVVU1JYNHNyWGh4YVNyLzg1UHhQYUJNbTVGWU5YaG5kZkkxZFBaSTFTR3ZzbWtHSW1WamdjSDhYem1qN2tmME80SHBxWHhCN3QrdWVMNVhrb2VYczVxSzZqMXhYZkpKZkZUeGRvUWZuZmNVZlRzVnJkWVNZUkloQm10UWNib1VMUzNMUzF0bVZNN01mRlVia2pqYVh1Ty9iL1JSdmVxTHhNZDg4ZlFWZ0xHTVVuTTF3TldScmRpY3F4V1orMGZlVDJZYi81S0hpOEcyMkVaVWEyQnpYb21IUmtCSzBDQ09PU2pva2pya3FTZ3JzcTdnMlQ3em51VFd4dE9STlpiZ20zS0lsNzNiMDk1NGpzUHBFeVZWajZBbjVOQ09TMFB5dVpaQ1FFem9CR3JoNUQ4bUc2bHVjZkhWTkFKamRheHgrSHEyeE1tNDdkTkxNVHptTWEwbVF0ekpUQVFwZnNwZU1JY0F6Um9NaEJ3VTJ6RE53aGNoWkRoSGpNRkgyblBGVmNIUEpRNlRDYUFxUjBUcWxmb2hSc1hjczAwbUFpMDhvdllVWStGYncvY09jWkdUS3VmYjkiLCJtYWMiOiI4YWFjNWYyNzRmOGVmYTZmZTU3YTJjM2E5ZTRjMDMwZTE5N2VmZDc4YTZiZDU2OWFhMTZhMTU1NzNmNTgwNjliIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9wajNqSXFyQ1VuWEpzcHhTY2gzOGc9PSIsInZhbHVlIjoidElRclBsdSsvUkRxOTVmS1R4NzhEWGhMYjJ6MzdCVDY1a2J6ZUlwNzlERVZKWno5bllpKzR2OHdEdW9pVk9xdTI2V1VoSjhGMTNiZHlTUzk5RVd1NzIwdnVSdVlLYkpDWjhUMVpSbU9icVFvaG5XWW1udVhMbVA4UXBlRE1yQTlWeStERDhkMFlKZnByNlhZcDg4UzQrcTdwOEtmL00xY2JnZEZSSHFOamx0cHNDSVFxVWUrT0tyNVdjd1Zhbm00Q2YwRzVoVnZTcXp1R3V2RGprTXVvU1lpVnlTTURtem0xRWpIZFhoazhRVFdXV2FrVWJWR0JnU01RcmIxQ2FaTkV3QUlkeHFTQ2M2eGUvK3cvOXA5SUdaQmYyK2I2WUplOHc0YW1IVnI3azMrWDIwT2JIalVybUJsMmVLd2thMHNuZTVPY1EyTkRoYUpRMVFWdThTZ0xiY3YxekJUU2tudzRrMlVZdG84TUFDSWJiMHdlSFhHMkN5bHBlQTdibDFtSFUrbWdjS3cvQ2paaUI0MERuRlIrU3pBcnFqUDJCS3l3SCtGMGtVZHlGU1VBemlHNW1iUVUwcGVpckRTTW56bkh4WnRBbzZXeHB1SkxqMEN4dkpNOFFycDRYS0NPQXluZXlzVzJvVHlyNEQwdzFDaDhSMjh2djB2STVtN1JURkUiLCJtYWMiOiI1MjZkMzUxMmY1ODNkOTU0ZDg4OGRiMDRhMDAwNTRiOTUwYjI2MDMyNDNjZTFlZTM2MzE1NmQ3M2Y3MWI5YWNlIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpTZEUzeTQ0OHNmYit2UlNSa3NWWlE9PSIsInZhbHVlIjoiMFh4VGExME84YWlJbnU3d0FiR0Q5cXBPWmFhOE9TK2lLRkRhN2dOTHRkQm5kK0ROeTFTSmpUdWt3WXdQMVNWalJlcS9ldWFISC9SYWVVU1JYNHNyWGh4YVNyLzg1UHhQYUJNbTVGWU5YaG5kZkkxZFBaSTFTR3ZzbWtHSW1WamdjSDhYem1qN2tmME80SHBxWHhCN3QrdWVMNVhrb2VYczVxSzZqMXhYZkpKZkZUeGRvUWZuZmNVZlRzVnJkWVNZUkloQm10UWNib1VMUzNMUzF0bVZNN01mRlVia2pqYVh1Ty9iL1JSdmVxTHhNZDg4ZlFWZ0xHTVVuTTF3TldScmRpY3F4V1orMGZlVDJZYi81S0hpOEcyMkVaVWEyQnpYb21IUmtCSzBDQ09PU2pva2pya3FTZ3JzcTdnMlQ3em51VFd4dE9STlpiZ20zS0lsNzNiMDk1NGpzUHBFeVZWajZBbjVOQ09TMFB5dVpaQ1FFem9CR3JoNUQ4bUc2bHVjZkhWTkFKamRheHgrSHEyeE1tNDdkTkxNVHptTWEwbVF0ekpUQVFwZnNwZU1JY0F6Um9NaEJ3VTJ6RE53aGNoWkRoSGpNRkgyblBGVmNIUEpRNlRDYUFxUjBUcWxmb2hSc1hjczAwbUFpMDhvdllVWStGYncvY09jWkdUS3VmYjkiLCJtYWMiOiI4YWFjNWYyNzRmOGVmYTZmZTU3YTJjM2E5ZTRjMDMwZTE5N2VmZDc4YTZiZDU2OWFhMTZhMTU1NzNmNTgwNjliIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9wajNqSXFyQ1VuWEpzcHhTY2gzOGc9PSIsInZhbHVlIjoidElRclBsdSsvUkRxOTVmS1R4NzhEWGhMYjJ6MzdCVDY1a2J6ZUlwNzlERVZKWno5bllpKzR2OHdEdW9pVk9xdTI2V1VoSjhGMTNiZHlTUzk5RVd1NzIwdnVSdVlLYkpDWjhUMVpSbU9icVFvaG5XWW1udVhMbVA4UXBlRE1yQTlWeStERDhkMFlKZnByNlhZcDg4UzQrcTdwOEtmL00xY2JnZEZSSHFOamx0cHNDSVFxVWUrT0tyNVdjd1Zhbm00Q2YwRzVoVnZTcXp1R3V2RGprTXVvU1lpVnlTTURtem0xRWpIZFhoazhRVFdXV2FrVWJWR0JnU01RcmIxQ2FaTkV3QUlkeHFTQ2M2eGUvK3cvOXA5SUdaQmYyK2I2WUplOHc0YW1IVnI3azMrWDIwT2JIalVybUJsMmVLd2thMHNuZTVPY1EyTkRoYUpRMVFWdThTZ0xiY3YxekJUU2tudzRrMlVZdG84TUFDSWJiMHdlSFhHMkN5bHBlQTdibDFtSFUrbWdjS3cvQ2paaUI0MERuRlIrU3pBcnFqUDJCS3l3SCtGMGtVZHlGU1VBemlHNW1iUVUwcGVpckRTTW56bkh4WnRBbzZXeHB1SkxqMEN4dkpNOFFycDRYS0NPQXluZXlzVzJvVHlyNEQwdzFDaDhSMjh2djB2STVtN1JURkUiLCJtYWMiOiI1MjZkMzUxMmY1ODNkOTU0ZDg4OGRiMDRhMDAwNTRiOTUwYjI2MDMyNDNjZTFlZTM2MzE1NmQ3M2Y3MWI5YWNlIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275968614\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}