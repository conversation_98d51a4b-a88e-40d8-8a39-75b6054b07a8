{"__meta": {"id": "Xa0e5ee3e87f07dfa43bfb83518d6c69d", "datetime": "2025-07-12 15:49:04", "utime": **********.078827, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752335343.603706, "end": **********.07884, "duration": 0.47513413429260254, "duration_str": "475ms", "measures": [{"label": "Booting", "start": 1752335343.603706, "relative_start": 0, "end": 1752335343.978822, "relative_end": 1752335343.978822, "duration": 0.37511610984802246, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752335343.978829, "relative_start": 0.3751230239868164, "end": **********.078842, "relative_end": 1.9073486328125e-06, "duration": 0.10001301765441895, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48170376, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.026790000000000005, "accumulated_duration_str": "26.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.013885, "duration": 0.02153, "duration_str": "21.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.366}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.044078, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.366, "width_percent": 1.792}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.0588582, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 82.158, "width_percent": 1.792}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0608041, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.949, "width_percent": 1.12}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.065686, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 85.069, "width_percent": 9.406}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.070315, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.476, "width_percent": 5.524}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1524517956 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524517956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.064502, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-40302637 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-40302637\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-927546735 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-927546735\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1881903657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1881903657\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1khr1t7%7C1752334032243%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iitnakd1UVhNaitGTnVITlBnZjM1Mmc9PSIsInZhbHVlIjoibGpEREtUREhHa2RxYWFEa0QzWU9tcEFiMU53TFkzUERRdmhpTTFBQ05oUmlYVWRmb3RtemFUazZLamxXRFJ6NTVGRm45N1djU2pMWkZNSXpTdFdaa0JHUWZDZXNZNC9jZlJCU1hQT0pqdzRLdE4wS3psbE43amNSa0VzMUlwckhSTmJxUGJXYm9KemJwTUZLTjRLc1JuQ0M3cFZ1MWFtQm80RjJRSFVPN1FUeVYxZEpxR2licjdoWTk1OUx6MmVSMjJEd1lEN0VvSit5NmcwdEJLUEl1ay9MOVh5WkxmazFJdTRZdVNaUEZpeWt6VWRVTjM3ZEsxSkxMcmxuV0FBWmNkdnhvVVpSV2hONWhORjhCSzdEdVArazJBWHlVY2NQYUpoYUVzVjV2ZklheXhRdWV1QnJqdXpvRFV5WllkdW45NkpubUUrSHV4SVYxSXM0ejdQYy82QzNWV2NVTnhOazQ5cGZDNVZxdFhUcG1pN3dub3B3c2xac0FLNEZubk56anp6Y29NVUs0aVpmSjJDaG5BVDh2Kzc4WEVwZ2ZrY0E4RXBVT0RNY2Yvbyt3Q0tobUxuOHZNWCtGaEZTSHh5czd3QVlIL09TU3hla2UvSWZJdTJLM29PcCtvRHloRTg0UTlTM1FPZGVpc2s1RlU1NTFZSnUzZjA2VWlvUWdtNEkiLCJtYWMiOiJhZjFmNmZmNWRkMzYwMGRjNDc3NTRmYzk4OGZhZmYzOGFhOGU1YTU5MDE3YjcwMjI0NzE3ZGE5NWE0ZmI3N2RjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikd4U3pvT3k0U2pFOUJHV2k0cFEyTHc9PSIsInZhbHVlIjoidnZnbmRaZW5SaGJ6V0RTN1lRa3JORzlmQlpWU3FnaHNBRGI3M3lHaWxBZ3ZITUFNSURibkFjcFp0NkszWmhjSjVIeVlGVU9KNUErQksrRFJRL25ZL0tJSXdXSGxaeGRMQXVWdzMrOGtnazYrR242K0VPVVA1bzBLNmVhQTdmSlRsZmQyNjlWSWxuUk5PbDA4ZUs5NTZSZVFEcTFEQkZ3aDlINlkyamY1REwrTDlwS1hrK0krd1VJQ0o2Y2U1MGNCRkZuTVk3U2ZYMTl2WGNwejdVNlZXWWxLTFJNRDQ2alh3UVYzTmtzNUNiaVNMdGFaU25CR0Q1Q3ZmNCt2Y01oQ3FWRUsxQmdkT2s1QjZVYkhjZEhydnRkU3haZ1dRamVQcm9PdVVrbzNhSkxrVHg5OE44SmlQVWozYjZjTmJNSzh1OVBsd0JHY0MvZzBLMUpuWW5DWi9rNFB5ZjZGamR5U2VsN25yQ1htdzMrYlVsMlIxZXpzaGNuTVFYOE0xVXJIL2NoMThEQU10NXlhZVdxbEoxR0JKZ0lzWGhlOEkzcFZWSGtBK1FXZGVvZWdLb2dKUnZVcUN6YnFWc2dUcVovdEpuc0V4SHZGMVFjZTM4b0xKa1QzbGpVYUtvSVVmY1dTUWQxZDhpZkJ2MWRrTmZLQlpWaVRXRWVLSVJpUmR2RysiLCJtYWMiOiI0MzFlZjE1NDkzODFhYjNiYWFhN2RhMWM1ZTAyYmFjOGJmMTU1MWY4M2U2ODYzZDBiM2YwMzQxMzhiMGEzZTQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-119467486 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119467486\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 15:49:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklaSDc4RzRNZlRBbHJrYjRVdzVsQmc9PSIsInZhbHVlIjoiTTRpaHZEbExIWkYwM09nVGVYQ21pU1lnZitrTmd4bGMwTTJWVmluZXZpNUJ4d1NxY2dyUWN6ZkRDTGtMUXNjZytOMGxYM1I3SVFwT0d4L0pWRjF5TjBOVTNvWXgxdkVPWFpTRHVlaWljWWZ5aFhJZyt6b1lmU3NUejdlNHV1YlNWVjFWUTJUQVdzeHpjNnowQ2dYVk91M1VtMjhKUEFKU2p0T1QyK3dUb2tsSjB5ZysxVEJRQjBIKzlscjV1bXJKUUNwL3VsOW5VV3Q5ZU9WTzRmVEFOTzA5UUwxV1pDRGtpakYraEpMSmZ4TGs4dWIvNVUrZUM1UTBjOUhvbjFmek1nQXJkSHRyYnM0TDBxd0VhckhqSVcrNlU3a29mbXZhL08wMHFReThPVCtQc2JsR0N5MTdxeENBWFB1L2lieDhiUElXd3IybEZDK3FkSkloWVlFL1Bvd2hOSm03YWUwMUVyTWVHNW50aGxZbHcwU0RGelIwV2JiTmtEZ0FnSXJrTGVSS3lRaTZJRVk4ZWNUcnVVd0doM1FENStPYnZWVGNhZXhsQ0xkK0hJQnZZdFBXNzFQa0pvOUxFNlgxa2k5ZXA4U1BOWTBsYlRtei8wWC94KzZVd05XK1RkQmdwa2c1VExQaXJvV09rdXBvdVBTVm9USzJhMGR1ZDFEZG1oZ1oiLCJtYWMiOiI1MmI2YmNiZDE2OTc4NDc5ODZhOThiMmUxYTAzNTVlODM1Njk3N2FkOTAwOTliZDNjNjJmZjk0MzU4NDYyNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:49:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im8wdnFwN1JJSXl4VXdETmJ4cDNsYWc9PSIsInZhbHVlIjoiMEtJM3I3R1NhVVd6ODd0dE5XS2ZLaDVIY3F5S00yZDJ2YzBsc2luK2I2RTFnRnB6TmwvM0F6Y1BqTTR3Z09BejdvcmE0d1ZjTmRsaThhZkJ0L21uS1JOaCsyRWVjZzdxQXA2Y0kya2VqeW45NFk5OUFZNkx6SmVmcnRORm1ySS9HZU1rZlJHcjYzeWNlRW9mQkUyM0RCSjdoQ21SMm9RVmt3eU4vM2hnVnhMdmJtaWR3NlRiZy8yNlg5SnhYekdQRXloejdDNzZyT0lHNHRkUkdRd3VpdHNaT1hEcnQ0MENPU29XK0NYanVVQ0RsaHVaUGJPcmNaVVFqM3RXeUVzVTNuVVl0cFU5Ty9VRHNQR1hxOVg0YmpKeFBaaFVOcHcxWUh0bHFjR3ZjekpTVTlPVWkvekRPYnhHaTdNVVc1UHZ3eGttNnczckFtSVZLVnNCMlAwV0hTK3MvbXBGT2lCNHJOeUk1eXk4bzFYWWJVOW1zcFBsNG96ckpVUWx0dGhkSGFhSEpnM2MxSW5IR1hLbXVrUGtQTTV4U3BoMlI4UE5xckE5ZGhweTQrTWxWcU12NUxEZm1NemNyakVSV2xQQytYYnB3VC94NTZUYmRNZ0pwT29pM25tMHRrdlJuVU4zLzdrY2t2VFBqaXp2bEZQTXNTa0s5cUZJdUZJV013aGIiLCJtYWMiOiJlMDZiODE2ZTY2MGUwMGE5YTNkZWQzZmI2OWU4M2ExNTZiYTM4N2NjZmIzMzdjMDNjOTk3Yjk3MThjZmE5MTMyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 17:49:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklaSDc4RzRNZlRBbHJrYjRVdzVsQmc9PSIsInZhbHVlIjoiTTRpaHZEbExIWkYwM09nVGVYQ21pU1lnZitrTmd4bGMwTTJWVmluZXZpNUJ4d1NxY2dyUWN6ZkRDTGtMUXNjZytOMGxYM1I3SVFwT0d4L0pWRjF5TjBOVTNvWXgxdkVPWFpTRHVlaWljWWZ5aFhJZyt6b1lmU3NUejdlNHV1YlNWVjFWUTJUQVdzeHpjNnowQ2dYVk91M1VtMjhKUEFKU2p0T1QyK3dUb2tsSjB5ZysxVEJRQjBIKzlscjV1bXJKUUNwL3VsOW5VV3Q5ZU9WTzRmVEFOTzA5UUwxV1pDRGtpakYraEpMSmZ4TGs4dWIvNVUrZUM1UTBjOUhvbjFmek1nQXJkSHRyYnM0TDBxd0VhckhqSVcrNlU3a29mbXZhL08wMHFReThPVCtQc2JsR0N5MTdxeENBWFB1L2lieDhiUElXd3IybEZDK3FkSkloWVlFL1Bvd2hOSm03YWUwMUVyTWVHNW50aGxZbHcwU0RGelIwV2JiTmtEZ0FnSXJrTGVSS3lRaTZJRVk4ZWNUcnVVd0doM1FENStPYnZWVGNhZXhsQ0xkK0hJQnZZdFBXNzFQa0pvOUxFNlgxa2k5ZXA4U1BOWTBsYlRtei8wWC94KzZVd05XK1RkQmdwa2c1VExQaXJvV09rdXBvdVBTVm9USzJhMGR1ZDFEZG1oZ1oiLCJtYWMiOiI1MmI2YmNiZDE2OTc4NDc5ODZhOThiMmUxYTAzNTVlODM1Njk3N2FkOTAwOTliZDNjNjJmZjk0MzU4NDYyNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:49:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im8wdnFwN1JJSXl4VXdETmJ4cDNsYWc9PSIsInZhbHVlIjoiMEtJM3I3R1NhVVd6ODd0dE5XS2ZLaDVIY3F5S00yZDJ2YzBsc2luK2I2RTFnRnB6TmwvM0F6Y1BqTTR3Z09BejdvcmE0d1ZjTmRsaThhZkJ0L21uS1JOaCsyRWVjZzdxQXA2Y0kya2VqeW45NFk5OUFZNkx6SmVmcnRORm1ySS9HZU1rZlJHcjYzeWNlRW9mQkUyM0RCSjdoQ21SMm9RVmt3eU4vM2hnVnhMdmJtaWR3NlRiZy8yNlg5SnhYekdQRXloejdDNzZyT0lHNHRkUkdRd3VpdHNaT1hEcnQ0MENPU29XK0NYanVVQ0RsaHVaUGJPcmNaVVFqM3RXeUVzVTNuVVl0cFU5Ty9VRHNQR1hxOVg0YmpKeFBaaFVOcHcxWUh0bHFjR3ZjekpTVTlPVWkvekRPYnhHaTdNVVc1UHZ3eGttNnczckFtSVZLVnNCMlAwV0hTK3MvbXBGT2lCNHJOeUk1eXk4bzFYWWJVOW1zcFBsNG96ckpVUWx0dGhkSGFhSEpnM2MxSW5IR1hLbXVrUGtQTTV4U3BoMlI4UE5xckE5ZGhweTQrTWxWcU12NUxEZm1NemNyakVSV2xQQytYYnB3VC94NTZUYmRNZ0pwT29pM25tMHRrdlJuVU4zLzdrY2t2VFBqaXp2bEZQTXNTa0s5cUZJdUZJV013aGIiLCJtYWMiOiJlMDZiODE2ZTY2MGUwMGE5YTNkZWQzZmI2OWU4M2ExNTZiYTM4N2NjZmIzMzdjMDNjOTk3Yjk3MThjZmE5MTMyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 17:49:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}