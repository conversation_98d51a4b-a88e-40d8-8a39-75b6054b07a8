{"__meta": {"id": "Xfed7cf174f49b016397b34cff727750c", "datetime": "2025-07-12 16:06:56", "utime": **********.50692, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.022055, "end": **********.506934, "duration": 0.4848790168762207, "duration_str": "485ms", "measures": [{"label": "Booting", "start": **********.022055, "relative_start": 0, "end": **********.43145, "relative_end": **********.43145, "duration": 0.4093949794769287, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.431459, "relative_start": 0.40940403938293457, "end": **********.506936, "relative_end": 2.1457672119140625e-06, "duration": 0.07547712326049805, "duration_str": "75.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45712120, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023159999999999997, "accumulated_duration_str": "23.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.461188, "duration": 0.02175, "duration_str": "21.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.912}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4917312, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.912, "width_percent": 2.677}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.497557, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.589, "width_percent": 3.411}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Ikx2ekhhcWVvYTNaSWkwV2dVWS9pTnc9PSIsInZhbHVlIjoidkhTR2RybExGMERhanFaaUVHYXBXdz09IiwibWFjIjoiYWJjNzdjMzZjMjg3NTgyYjY5YmZiYjYyZTk3YTU5NTQyYjkyMTRhZmI2YzM0MjBkODA3Y2NmYjQwZDlmNmQ5NiIsInRhZyI6IiJ9\"\n]", "pos": "array:1 [\n  2421 => array:9 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"2421\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1286361089 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ikx2ekhhcWVvYTNaSWkwV2dVWS9pTnc9PSIsInZhbHVlIjoidkhTR2RybExGMERhanFaaUVHYXBXdz09IiwibWFjIjoiYWJjNzdjMzZjMjg3NTgyYjY5YmZiYjYyZTk3YTU5NTQyYjkyMTRhZmI2YzM0MjBkODA3Y2NmYjQwZDlmNmQ5NiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; _clsk=1eejy7q%7C1752336375311%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpYY2pSTjczdklYMEU4V3RrZ3Rsd2c9PSIsInZhbHVlIjoiMXhNYXovQ1FlckptN3hmRzJGZ1VzR3RGTkV3LzBTZ2JOb2pYNjgzZVU1RFZhZ25objRFcHA5SGRsRFhQYS92VnFyS0JjNk1nblB3aGNtaGNDSEZjVUZOTk9CYTIzdFpHWjl2TXhieThOa0JwUTBYOXdSK1BUbWIxWTNUc04yWWNIOGZ4MUp4SlV3VFVBZ2QxL2NkNWF3QTRUdks5NHFUbzJxOWIrcUhQQkIwOEp2OGlma0N1ZlRXbUQ3eXpOMjY3ZTJTeDBEbit2emtXWkR1RUxlNkx1cUpFbnEzT1lTc3RFUEcxQTJLaUtoQ1prVzRVeGlwdTdKblJLMzF4K3doSndpT0Y4Y1lIVnJESk5TRTZqU2pPbkVzcXJIejhzKzYzSHgwZVhyOXYwVDdHRHdRejdHd2liaDBzVlI4d1BWanJKOEgranZ5VlJqOENCS2VnejRBR3ZnWFlQRUVsVFBIYjVFSjVJaWMxc1hUTk1rWkZmNi9YWEZYVGZ3WG94ZG1wMnNBKzByTDlEekRiS2N6WE01OE5QZnE2L2dhOFlrLzlXZm9VTGtkTzNGSmNnNXk2MzNiZGpCN2U0aFBnTDVjVTlSSTFMQm5sdTJBZDAwamhaM3JoSmZnWG00QXIrckVMdXJHSFZaSEtXSW9iNXQxWmJqZnJzZkhJSU9JRU1INXMiLCJtYWMiOiJjYjdlZDgyNmVkNjhiZmU4NzI3ZWQyYzU3YzE3MjQzYWRiNzUwYzQ4ZjA4ZGY2NmRkMjc5NTJmZTI2YTlkNDkxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBOODlZdkoxZVBuZ0xZdEZtSmF5WEE9PSIsInZhbHVlIjoibFh5cFRsT2tkU2llYVhEYTl2Y1lNV2JrSHVmeGZNK1F2K0pQTUcxTG1ISWpTYW9LOW4wRThTK2hzaFNUSHVodWJmTkc1Z2Q1WVNCek13M3RNa3E0K1hwYXgwYysySEtLZU1nWHFsQlBRQ3luT0FiMDVoeE5QbEVTa3B4djNITnRHL3B2eUxLS2VISjB6ZVg4K1l5RTcvLytPUlFsYUtZcTJmcGtTYlR1eDU1QTRkelNFSWJEeEZEQkZ2UHBqOVV0emh3eGc1SElBVmlPTzFiSlhzVWV4WFpKWUptTisyUTFoVitFZzN4MnVGVGU2U0pJS2VuU1JabEw5aHVEWnFvWHNJUFZKVlJ1S21uUkhxekVFRnF3UmNOVHdGK0hTbjhzSTlsMGRkKzhDQXlLU0U0M3BMTzJIV1lnUU10NHFMek5BY3FIT3R0QmhKMi9rMGdRcXcyRXZySnMwdXdya0xBUHdjd1IyZVlCaFpESFJsYndBWEpFTEtTQ3pydEhGVTV3dEIzam04UUpjcGlmYVBoNHY0aDYxWGNwbzRpamoyQ3VrOWRvdFZ4ZERENWU2czJSSGFZbVZWZ0YzU1NTUVI1NmxIOW9TMmdqcVNpdmN2b1RyaW0rS1R4S0xYbFdWbmZtdzM5OTdXa2pIU21ZMFFrZnR2ZUtUcGhlSXFhd1ZGMGsiLCJtYWMiOiJkOGE3ZmNkMWQ2MTE1OWU0NTVlOGU5MzJiMmIwZGE0MmJiM2RlMGNlODhlNGNjMzUzNDBkNDhhNDUyZGU4OTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286361089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2066980318 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066980318\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1611579585 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:06:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxlR0FhU3dmbGk2c0lBdUxCd2xFcUE9PSIsInZhbHVlIjoicVphdHpSSWZsL1NTRlJYMjBoaDNoWXNkenFkQSsyL1R0dkJIS294alFpUU1vcC9BdExacmZjSXFqb1diMm1LUWFTOC9SYWJHRWkzNjFhNXRpUjZyZUdKSG15RnlFU29naWp2eTNKQk5VajQ2OW5RYkx5Nkg0M1JwSDlNclMrTkRITzdKZGlTYU1yamVDY3lrTE83SENyQ1hEeGZFVW5zeFc1QXZ6MHlwK1Ewcm1QczdYTUREQ0VPZnNsOTBFRzVFVFNzQkVSRkoybmNGOXdjQUZzMUR0U2JLdzlTYTdEQlc1YVJRYWdHVkxXalRtbG5RMStQSGRTUXVwcnUzanhuSDRLcGY4enV5MTBUQ3NPa2tPS1oxa1M5b05mcElGanNwb0RmRklrNk1OczZuc3FUV24yekgyNkZOdm54c0g1Y29DYS9rLzdRei80aUR5dWk1MENQUDVVNWx6ZGhDa1VVZ2x2RitXSDdlUE9HSzN2QzhoMisxRFZQeDdIUGVmNUIyNFFERXBNcHg1YmVCaVRrYng0bExwRm8zNTZ2ZzVqTjFYNEl2bE9MZVdUdnYzL3FBVGZJUTdSbFlrNFR2d1l3aEFOdXkycUxMTG4xSDZvcFQ0N1pTOFl1dGVIenF1YjJrN3NIeW56S1gyMUxpVm5WTnhmbWVsOUIvaFBrdms0cWwiLCJtYWMiOiIyNWYyZTJkM2JiZTQ4YTkxYTU3NzEwYzk5Y2NlYzNmNDBjMGRhNGI4MTQ2NTk0Y2M2MjRlZmY5NTJiMTJlMmM3IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9DNTNzd0NYNGF1bUR6cVZiaVRDV2c9PSIsInZhbHVlIjoiYzlUQVNXdkZXOXRjd0VUV0tUV2ZnNm9VSTFlUHd6VWxWTDJ2aUJ1QjYwU1BZUEk5QlU5UXcwdTJRRWJybjNvOU5idGdPQzNZOXcyNzA5WXJPL2ZtU0Ezc1VGZU1ER3hKWlltRzZSSGRFUTVzdEwzSS9pRWNkQjNEY2RqYmplRGU3R1VieHh1cmM3dXRKaG1zTHY0S0xBL1p0L0grdmhFWWczQ1JpZUxNWlZxZ0J1RE1xV3hNUGU5VlYxd2NLVW85RE9nby9uTUcrajBJTWFIRFN3blR3ZVRURDNWeFNYbEJrQk0rY085eTYraCtNS3N3dVJ5WXFpaDBlS004MTFsVC82YnA1aEFOSUszMHhObmRJeVZJSzM5ek5tSzh1WklodyttcXFjMTREU1A2ZXNZQlcvZ2s3TjdaakhKNGdMRFlzTmRhcjRNeDJUZUVFdFZyMDdWcTE0MlNSVC9oSGtiN2wxNm1tNGdBeDNlVy95aGh0eEc1TlliODVscmNvazNETHlMQWpmb2YrRDIxemZNK1dRQ0Mvb0hPTlorZUZFenVzZ0g1aFNEREh3WXhZS21RK1FoU1BNWTBHOXI0Kzc3OENTdFAwN2l4K0ZNRHkwZU54ZFZSNVg5T1ZySFBUZ3BGeHM4MmVyUnBXeWNmam9id1JJVjlJeDAxVlFzbVdOa00iLCJtYWMiOiJlNDNjMzVkMTg0ZGYzNDY3OGVkOGY3ODE0MDlmYTRiNTkxMGNiYTIwNzJiYTJhYzJkNGM0ODkyY2EwNjQ3NjU5IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:06:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxlR0FhU3dmbGk2c0lBdUxCd2xFcUE9PSIsInZhbHVlIjoicVphdHpSSWZsL1NTRlJYMjBoaDNoWXNkenFkQSsyL1R0dkJIS294alFpUU1vcC9BdExacmZjSXFqb1diMm1LUWFTOC9SYWJHRWkzNjFhNXRpUjZyZUdKSG15RnlFU29naWp2eTNKQk5VajQ2OW5RYkx5Nkg0M1JwSDlNclMrTkRITzdKZGlTYU1yamVDY3lrTE83SENyQ1hEeGZFVW5zeFc1QXZ6MHlwK1Ewcm1QczdYTUREQ0VPZnNsOTBFRzVFVFNzQkVSRkoybmNGOXdjQUZzMUR0U2JLdzlTYTdEQlc1YVJRYWdHVkxXalRtbG5RMStQSGRTUXVwcnUzanhuSDRLcGY4enV5MTBUQ3NPa2tPS1oxa1M5b05mcElGanNwb0RmRklrNk1OczZuc3FUV24yekgyNkZOdm54c0g1Y29DYS9rLzdRei80aUR5dWk1MENQUDVVNWx6ZGhDa1VVZ2x2RitXSDdlUE9HSzN2QzhoMisxRFZQeDdIUGVmNUIyNFFERXBNcHg1YmVCaVRrYng0bExwRm8zNTZ2ZzVqTjFYNEl2bE9MZVdUdnYzL3FBVGZJUTdSbFlrNFR2d1l3aEFOdXkycUxMTG4xSDZvcFQ0N1pTOFl1dGVIenF1YjJrN3NIeW56S1gyMUxpVm5WTnhmbWVsOUIvaFBrdms0cWwiLCJtYWMiOiIyNWYyZTJkM2JiZTQ4YTkxYTU3NzEwYzk5Y2NlYzNmNDBjMGRhNGI4MTQ2NTk0Y2M2MjRlZmY5NTJiMTJlMmM3IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9DNTNzd0NYNGF1bUR6cVZiaVRDV2c9PSIsInZhbHVlIjoiYzlUQVNXdkZXOXRjd0VUV0tUV2ZnNm9VSTFlUHd6VWxWTDJ2aUJ1QjYwU1BZUEk5QlU5UXcwdTJRRWJybjNvOU5idGdPQzNZOXcyNzA5WXJPL2ZtU0Ezc1VGZU1ER3hKWlltRzZSSGRFUTVzdEwzSS9pRWNkQjNEY2RqYmplRGU3R1VieHh1cmM3dXRKaG1zTHY0S0xBL1p0L0grdmhFWWczQ1JpZUxNWlZxZ0J1RE1xV3hNUGU5VlYxd2NLVW85RE9nby9uTUcrajBJTWFIRFN3blR3ZVRURDNWeFNYbEJrQk0rY085eTYraCtNS3N3dVJ5WXFpaDBlS004MTFsVC82YnA1aEFOSUszMHhObmRJeVZJSzM5ek5tSzh1WklodyttcXFjMTREU1A2ZXNZQlcvZ2s3TjdaakhKNGdMRFlzTmRhcjRNeDJUZUVFdFZyMDdWcTE0MlNSVC9oSGtiN2wxNm1tNGdBeDNlVy95aGh0eEc1TlliODVscmNvazNETHlMQWpmb2YrRDIxemZNK1dRQ0Mvb0hPTlorZUZFenVzZ0g1aFNEREh3WXhZS21RK1FoU1BNWTBHOXI0Kzc3OENTdFAwN2l4K0ZNRHkwZU54ZFZSNVg5T1ZySFBUZ3BGeHM4MmVyUnBXeWNmam9id1JJVjlJeDAxVlFzbVdOa00iLCJtYWMiOiJlNDNjMzVkMTg0ZGYzNDY3OGVkOGY3ODE0MDlmYTRiNTkxMGNiYTIwNzJiYTJhYzJkNGM0ODkyY2EwNjQ3NjU5IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:06:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611579585\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2132086608 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ikx2ekhhcWVvYTNaSWkwV2dVWS9pTnc9PSIsInZhbHVlIjoidkhTR2RybExGMERhanFaaUVHYXBXdz09IiwibWFjIjoiYWJjNzdjMzZjMjg3NTgyYjY5YmZiYjYyZTk3YTU5NTQyYjkyMTRhZmI2YzM0MjBkODA3Y2NmYjQwZDlmNmQ5NiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132086608\", {\"maxDepth\":0})</script>\n"}}