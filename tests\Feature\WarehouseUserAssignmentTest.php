<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class WarehouseUserAssignmentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test that user with assigned warehouse sees only their warehouse
     */
    public function test_user_with_assigned_warehouse_sees_only_their_warehouse()
    {
        // إنشاء مستودع
        $warehouse = warehouse::factory()->create([
            'name' => 'Test Warehouse',
            'created_by' => 1
        ]);

        // إنشاء مستخدم مع مستودع مخصص
        $user = User::factory()->create([
            'warehouse_id' => $warehouse->id,
            'created_by' => 1
        ]);

        // تسجيل دخول المستخدم
        $this->actingAs($user);

        // زيارة صفحة نقطة البيع
        $response = $this->get(route('pos.index'));

        // التحقق من أن الصفحة تحمل بنجاح
        $response->assertStatus(200);

        // التحقق من أن المستودع المخصص محدد
        $response->assertViewHas('warehouseId', $warehouse->id);

        // التحقق من أن قائمة المستودعات تحتوي على المستودع المخصص فقط
        $response->assertViewHas('warehouses', function ($warehouses) use ($warehouse) {
            return $warehouses->count() === 1 && $warehouses->has($warehouse->id);
        });
    }

    /**
     * Test that user without assigned warehouse sees all warehouses
     */
    public function test_user_without_assigned_warehouse_sees_all_warehouses()
    {
        // إنشاء عدة مستودعات
        $warehouse1 = warehouse::factory()->create(['created_by' => 1]);
        $warehouse2 = warehouse::factory()->create(['created_by' => 1]);

        // إنشاء مستخدم بدون مستودع مخصص
        $user = User::factory()->create([
            'warehouse_id' => null,
            'created_by' => 1
        ]);

        // تسجيل دخول المستخدم
        $this->actingAs($user);

        // زيارة صفحة نقطة البيع
        $response = $this->get(route('pos.index'));

        // التحقق من أن الصفحة تحمل بنجاح
        $response->assertStatus(200);

        // التحقق من أن قائمة المستودعات تحتوي على جميع المستودعات
        $response->assertViewHas('warehouses', function ($warehouses) {
            return $warehouses->count() >= 2;
        });
    }

    /**
     * Test warehouse assignment in database
     */
    public function test_warehouse_assignment_in_database()
    {
        // إنشاء مستودع
        $warehouse = warehouse::factory()->create();

        // إنشاء مستخدم
        $user = User::factory()->create();

        // تخصيص المستودع للمستخدم
        $user->warehouse_id = $warehouse->id;
        $user->save();

        // التحقق من التخصيص
        $this->assertEquals($warehouse->id, $user->fresh()->warehouse_id);

        // التحقق من العلاقة
        $this->assertInstanceOf(warehouse::class, $user->warehouse);
        $this->assertEquals($warehouse->id, $user->warehouse->id);
    }
}
